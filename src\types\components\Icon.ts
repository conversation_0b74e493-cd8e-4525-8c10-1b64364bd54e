import { SVGProps } from 'react';
import { iconSizes } from '../ui';
import * as LucideIcons from 'lucide-react';

// Get all Lucide icon names
type LucideIconName = keyof typeof LucideIcons;

// Re-export LucideIcon for type usage
export type { LucideIcon } from 'lucide-react';

/**
 * Type for all available icon names
 * This will automatically stay in sync with the actual icon components being used
 */
export type IconName = LucideIconName;

/**
 * Available icon sizes
 */
export type IconSize = keyof typeof iconSizes;

/**
 * Icon component props
 */
export interface IconProps extends Omit<SVGProps<SVGSVGElement>, 'ref' | 'name' | 'size'> {
  /** Name of the icon from the icon library */
  name: IconName;
  /** Size of the icon */
  size?: IconSize | number;
  /** Additional CSS classes */
  className?: string;
  /** Whether the icon should be rendered with a circular background */
  withBackground?: boolean;
  /** Background color when `withBackground` is true */
  bgColor?: string;
  /** Size of the background when `withBackground` is true */
  backgroundSize?: number | string;
  /** Color of the icon */
  color?: string;
  /** Whether the icon should be animated */
  animated?: boolean;
  /** Animation type */
  animation?: 'spin' | 'pulse' | 'bounce';
  /** Animation duration in seconds */
  animationDuration?: number;
}

/**
 * Props for the IconButton component (wrapper around Icon with button functionality)
 */
export interface IconButtonProps extends Omit<IconProps, 'name' | keyof React.ButtonHTMLAttributes<HTMLButtonElement>>, React.ButtonHTMLAttributes<HTMLButtonElement> {
  /** Name of the icon */
  name: IconName;
  /** Button variant */
  variant?: 'ghost' | 'outline' | 'solid' | 'link';
  /** Button size */
  size?: 'sm' | 'md' | 'lg';
  /** Whether the button is in a loading state */
  isLoading?: boolean;
  /** Loading spinner size */
  loadingSize?: 'sm' | 'md' | 'lg';
  /** Loading spinner variant */
  loadingVariant?: 'spinner' | 'dots';
  /** Loading text to show when button is loading */
  loadingText?: string;
  /** Whether the button is active */
  isActive?: boolean;
  /** Whether the button should have a tooltip */
  tooltip?: string;
  /** Tooltip position */
  tooltipPosition?: 'top' | 'right' | 'bottom' | 'left';
}
