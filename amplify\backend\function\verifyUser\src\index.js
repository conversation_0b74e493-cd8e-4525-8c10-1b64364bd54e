
const {
  CognitoIdentityProviderClient,
  AdminSetUser<PERSON>asswordCommand,
  AdminGetUserCommand,
  AdminConfirmSignUpCommand,
  AdminAddUserToGroupCommand,
  ConfirmSignUpCommand
} = require('@aws-sdk/client-cognito-identity-provider');
const client = new CognitoIdentityProviderClient({ region: process.env.AWS_REGION || 'us-east-1' });

exports.handler = async (event) => {

  const userPoolId = process.env.AUTH_MYVILLAGEFREELANCEB6232A5F_USERPOOLID;
  const clientId = process.env.AUTH_MYVILLAGEFREELANCEB6232A5F_USERPOOLWEBCLIENTID;
  let username, password, verificationCode, role;

  // Support API Gateway event structure
  if (event.body) {
    try {
      const body = JSON.parse(event.body);
      username = body.username || body.email;
      password = body.password;
      verificationCode = body.verificationCode || body.code;
      role = body.role;
    } catch (e) {
      return {
        statusCode: 400,
        headers: {
          "Access-Control-Allow-Origin": "*",
          "Access-Control-Allow-Headers": "Content-Type",
          "Access-Control-Allow-Methods": "POST, OPTIONS"
        },
        body: JSON.stringify({ error: 'Invalid request body' })
      };
    }
  } else {
    username = event.username || event.email;
    password = event.password;
    verificationCode = event.verificationCode || event.code;
    role = event.role;
  }

  if (!username || !password || !verificationCode) {
    return {
      statusCode: 400,
      headers: {
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Headers": "Content-Type",
        "Access-Control-Allow-Methods": "POST, OPTIONS"
      },
      body: JSON.stringify({ error: 'username, password, and verificationCode are required' })
    };
  }

  try {
    // First, confirm the signup with the verification code using the client-side method
    const confirmCmd = new ConfirmSignUpCommand({
      ClientId: clientId,
      Username: username,
      ConfirmationCode: verificationCode,
    });
    await client.send(confirmCmd);

    // Set permanent password
    const setPwdCmd = new AdminSetUserPasswordCommand({
      UserPoolId: userPoolId,
      Username: username,
      Password: password,
      Permanent: true
    });
    await client.send(setPwdCmd);

    // Add user to the appropriate group if role is provided
    if (role) {
      const groupName = role || process.env.DEFAULT_USER_GROUP;
      const addToGroupCmd = new AdminAddUserToGroupCommand({
        UserPoolId: userPoolId,
        Username: username,
        GroupName: groupName,
      });
      await client.send(addToGroupCmd);
    }

    // Verify user status
    const getUserCmd = new AdminGetUserCommand({
      UserPoolId: userPoolId,
      Username: username
    });
    const result = await client.send(getUserCmd);

    return {
      statusCode: 200,
      headers: {
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Headers": "Content-Type",
        "Access-Control-Allow-Methods": "POST, OPTIONS"
      },
      body: JSON.stringify({
        message: 'User verified and confirmed successfully',
        userStatus: result.UserStatus,
        email: username
      })
    };
  } catch (err) {
    console.error("Error confirming user:", err);

    // Handle specific errors
    let errorMessage = 'Verification failed';
    let statusCode = 500;

    if (err.name === 'CodeMismatchException') {
      errorMessage = 'Invalid verification code';
      statusCode = 400;
    } else if (err.name === 'ExpiredCodeException') {
      errorMessage = 'Verification code has expired';
      statusCode = 400;
    } else if (err.name === 'UserNotFoundException') {
      errorMessage = 'User not found';
      statusCode = 404;
    }

    return {
      statusCode: statusCode,
      headers: {
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Headers": "Content-Type",
        "Access-Control-Allow-Methods": "POST, OPTIONS"
      },
      body: JSON.stringify({ error: errorMessage, details: err.message })
    };
  }
};
