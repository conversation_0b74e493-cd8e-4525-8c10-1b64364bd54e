import React from "react";
import { But<PERSON> } from "./Button";
import { Icon } from "./Icon";

export interface ConfirmDialogProps {
  /**
   * Whether the dialog is open
   */
  open: boolean;
  /**
   * Dialog title
   */
  title: string;
  /**
   * Dialog message/description
   */
  message: React.ReactNode;
  /**
   * Text for the confirm button
   * @default 'Confirm'
   */
  confirmText?: string;
  /**
   * Text for the cancel button
   * @default 'Cancel'
   */
  cancelText?: string;
  /**
   * Variant for the confirm button
   * @default 'destructive'
   */
  confirmVariant?: "default" | "destructive" | "outline" | "ghost";
  /**
   * Callback when the confirm button is clicked
   */
  onConfirm: () => void;
  /**
   * Callback when the cancel button is clicked or the dialog is closed
   */
  onCancel: () => void;
  /**
   * Whether the confirm action is in progress
   * @default false
   */
  isLoading?: boolean;
  /**
   * Additional class name for the dialog
   */
  className?: string;
}

export const ConfirmDialog: React.FC<ConfirmDialogProps> = ({
  open,
  title,
  message,
  confirmText = "Confirm",
  cancelText = "Cancel",
  confirmVariant = "destructive",
  onConfirm,
  onCancel,
  isLoading = false,
}) => {
  if (!open) return null;

  if (!open) return null;

  return (
    <div className="fixed inset-0 z-[100] flex items-center justify-center p-4 bg-black/50 backdrop-blur-sm">
      <div className="w-full max-w-4xl mx-auto px-4 sm:px-6">
        <div
          className="bg-card text-card-foreground rounded-lg shadow-sm border w-full"
          onClick={(e) => e.stopPropagation()}
        >
          <div className="flex flex-col space-y-4 p-6">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold leading-none tracking-tight">
                {title}
              </h3>
              <button
                onClick={onCancel}
                className="rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none"
                disabled={isLoading}
                aria-label="Close"
              >
                <Icon name="X" size="sm" />
                <span className="sr-only">Close</span>
              </button>
            </div>

            <div className="text-sm text-muted-foreground">{message}</div>

            <div className="flex justify-end space-x-2 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={onCancel}
                disabled={isLoading}
                size="sm"
                className="min-w-[80px]"
              >
                {cancelText}
              </Button>
              <Button
                type="button"
                variant={confirmVariant}
                onClick={onConfirm}
                disabled={isLoading}
                size="sm"
                className="min-w-[80px]"
              >
                {isLoading ? (
                  <>
                    <Icon
                      name="Loader2"
                      className="mr-2 h-4 w-4 animate-spin"
                    />
                    {confirmText}
                  </>
                ) : (
                  confirmText
                )}
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ConfirmDialog;
