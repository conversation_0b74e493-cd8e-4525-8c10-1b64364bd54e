export type JobStatus =
  | 'OPEN'
  | 'IN_PROGRESS'
  | 'COMPLETED'
  | 'CANCELLED';

export type JobCategory =
  | 'WEB_DEVELOPMENT'
  | 'MOBILE_DEVELOPMENT'
  | 'DESIGN'
  | 'WRITING'
  | 'MARKETING'
  | 'BUSINESS'
  | 'ACCOUNTING'
  | 'LEGAL'
  | 'OTHER';

export type ProposalStatus = 'PENDING' | 'ACCEPTED' | 'REJECTED' | 'WITHDRAWN';

export interface JobProposal {
  id: string;
  jobId: string;
  freelancerId: string;
  freelancerName?: string;
  coverLetter?: string;
  bidAmount: number;
  proposedRate?: number;
  status: ProposalStatus;
  createdAt: string;
  updatedAt?: string;
  job?: Job;
}



export interface JobWithProposals extends Job {
  proposals: JobProposal[];
  proposalCount: number;
}

export interface Job {
  id: string;
  title: string;
  description: string;
  category: JobCategory;
  budget: number;
  deadline: string;
  clientId: string;
  status: JobStatus;
  isRemote?: boolean;
  skills?: string[];
  location?: string;
  createdAt: string;
  updatedAt?: string;
  client?: {
    id: string;
    name: string;
    email: string;
  };
  proposals?: JobProposal[];
  proposalCount?: number;
}

export interface CreateJobInput {
  title: string;
  description: string;
  category: JobCategory;
  budget: number;
  deadline: string;
  isRemote?: boolean;
  skills?: string[];
  location?: string;
}

export interface UpdateJobInput {
  id: string;
  title?: string;
  description?: string;
  category?: JobCategory;
  budget?: number;
  deadline?: string;
  status?: JobStatus;
  isRemote?: boolean;
  skills?: string[];
  location?: string;
}

export interface JobFilter {
  clientId?: string;
  category?: JobCategory;
  minBudget?: number;
  maxBudget?: number;
  isRemote?: boolean;
  status?: JobStatus;
  searchTerm?: string;
}
