import { generateClient, GraphQLResult } from 'aws-amplify/api';
import { fetchAuthSession } from 'aws-amplify/auth';
import type { DocumentNode } from '@apollo/client';
import { print } from 'graphql';
import { Observable } from 'rxjs';

type AuthMode = 'apiKey' | 'userPool';

interface RequestOptions {
  authMode: AuthMode;
  authToken?: string;
}

interface AmplifyGraphQLClient {
  graphql: (options: {
    query: string;
    variables?: Record<string, unknown>;
    authMode?: AuthMode;
    authToken?: string;
  }) => Promise<GraphQLResult<unknown>> | Observable<{ data: unknown }>;
}

export class GraphQLClient {
  private client: AmplifyGraphQLClient;

  constructor() {
    try {
      this.client = generateClient() as AmplifyGraphQLClient;
    } catch (error) {
      console.error('Failed to initialize GraphQL client:', error);
      throw error;
    }
  }

  /**
   * Execute a GraphQL query or mutation
   */
  async execute<T = unknown>(
    operation: string | DocumentNode,
    variables: Record<string, unknown> = {},
    options: RequestOptions = { authMode: 'userPool' }
  ): Promise<T> {
    try {
      const operationString = typeof operation === 'string' ? operation : print(operation);

      const result = await this.client.graphql({
        query: operationString,
        variables,
        authMode: options.authMode,
        ...(options.authToken && { authToken: options.authToken })
      }) as GraphQLResult<T>;

      if (result.errors?.length) {
        const errorMessages = result.errors
          .map(e => e.message)
          .join('\n');
        throw new Error(`GraphQL error: ${errorMessages}`);
      }

      if (result.data) {
        return result.data;
      }

      throw new Error('No data returned from GraphQL operation');
    } catch (error) {
      console.error('GraphQL operation error:', error);
      throw error;
    }
  }

  /**
   * Execute a GraphQL query
   */
  async query<T = unknown>(
    query: string | DocumentNode,
    variables: Record<string, unknown> = {},
    options: RequestOptions = { authMode: 'userPool' }
  ): Promise<T> {
    return this.execute<T>(query, variables, options);
  }

  /**
   * Execute a GraphQL mutation
   */
  async mutate<T = unknown>(
    mutation: string | DocumentNode,
    variables: Record<string, unknown> = {},
    options: RequestOptions = { authMode: 'userPool' }
  ): Promise<T> {
    return this.execute<T>(mutation, variables, options);
  }

  /**
   * Subscribe to GraphQL subscriptions
   */
  subscribe<T = unknown>(
    subscription: string | DocumentNode,
    variables: Record<string, unknown> = {},
    options: RequestOptions = { authMode: 'userPool' },
    onData?: (data: T) => void,
    onError?: (error: Error) => void
  ): { unsubscribe: () => void } {
    const subscriptionString = typeof subscription === 'string'
      ? subscription
      : print(subscription);

    const subscriptionObservable = this.client.graphql({
      query: subscriptionString,
      variables,
      authMode: options.authMode,
      ...(options.authToken && { authToken: options.authToken })
    }) as Observable<{ data: T }>;

    const subscriptionInstance = subscriptionObservable.subscribe({
      next: (result) => {
        if (onData) {
          onData(result.data);
        }
      },
      error: (error) => {
        console.error('GraphQL subscription error:', error);
        if (onError) {
          onError(error);
        }
      }
    });

    return {
      unsubscribe: () => {
        subscriptionInstance.unsubscribe();
      }
    };
  }

  /**
   * Get the current authentication token
   */
  async getAuthToken(): Promise<string | null> {
    try {
      const session = await fetchAuthSession();
      const token = session.tokens?.idToken?.toString();

      if (!token) {
        console.warn('No authentication token found. User may not be signed in.');
        return null;
      }

      return token;
    } catch (error) {
      console.error('Error getting auth token:', error);
      return '';
    }
  }
}

export const graphQLClient = new GraphQLClient();

export default graphQLClient;
