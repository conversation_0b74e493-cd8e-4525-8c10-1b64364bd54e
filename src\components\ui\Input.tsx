import React from 'react';
import { cn } from '@/lib/utils';

export interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  error?: boolean;
}

const Input = React.forwardRef<HTMLInputElement, InputProps>(
  ({ className, type, error, ...props }, ref) => {
    return (
      <input
        type={type}
        style={{
          height: '48px',
          padding: '12px 16px',
          fontSize: '16px',
          fontWeight: '500',
          borderRadius: '8px',
          border: '2px solid',
          borderColor: error ? 'rgb(220, 38, 38)' : 'var(--input)',
          backgroundColor: 'var(--background)',
          color: 'var(--foreground)',
          width: '100%',
          display: 'flex',
          alignItems: 'center',
          transition: 'all 0.2s ease-in-out',
        }}
        className={cn(
          'placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-1 focus-visible:border-ring disabled:cursor-not-allowed disabled:opacity-50 hover:border-ring/50',
          error && 'border-destructive focus-visible:ring-destructive focus-visible:border-destructive',
          className
        )}
        ref={ref}
        {...props}
      />
    );
  }
);
Input.displayName = 'Input';

export { Input };
