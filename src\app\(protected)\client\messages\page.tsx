'use client';

import { useAuth } from '@/lib/auth/AuthContext';
import { MessagingPage } from '@/components/messaging/MessagingPage';
import { Conversation, Message } from '@/components/messaging/types';
import { UserRole } from '@/types/enums';

export default function ClientMessagesPage() {
  const { user } = useAuth();
  
  // Mock data for conversations
  const mockConversations: Conversation[] = [
    {
      id: '1',
      participants: [
        {
          id: user?.username || 'client1',
          name: user?.attributes?.name || 'Client',
          email: user?.attributes?.email || '<EMAIL>',
          role: UserRole.CLIENT,
          isOnline: true
        },
        {
          id: 'freelancer1',
          name: 'Freelancer One',
          email: '<EMAIL>',
          role: UserRole.FREELANCER,
          isOnline: true
        }
      ],
      lastMessage: {
        id: 'msg1',
        content: 'Hi there! I\'m interested in your services.',
        senderId: user?.username || 'client1',
        timestamp: new Date(),
        status: 'delivered',
        type: 'text' as const
      },
      unreadCount: 0,
      updatedAt: new Date()
    },
  ];

  const handleSendMessage = async (conversationId: string, content: string) => {
    console.log(`Sending message to conversation ${conversationId}:`, content);
    return Promise.resolve();
  };

  const handleLoadMoreMessages = async (conversationId: string, before: Date) => {
    console.log(`Loading more messages for conversation ${conversationId} before`, before);
    return Promise.resolve([] as Message[]);
  };
  
  if (!user) {
    return (
      <div className="h-full flex items-center justify-center">
        <p>Loading user data...</p>
      </div>
    );
  }

  return (
    <div className="h-[calc(100vh-8rem)]">
      <MessagingPage
        currentUser={{
          id: user.username,
          name: user.attributes?.name || 'Client',
          email: user.attributes?.email || '',
          avatar: Array.isArray(user.attributes?.picture) ? user.attributes.picture[0] : user.attributes?.picture || '/default-avatar.png',
          role: UserRole.CLIENT,
          isOnline: true
        }}
        initialConversations={mockConversations}
        onSendMessage={handleSendMessage}
        onLoadMoreMessages={handleLoadMoreMessages}
        className="h-full"
      />
    </div>
  );
}
