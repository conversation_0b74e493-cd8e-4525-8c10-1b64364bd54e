'use client';

import { usePathname, useRouter } from 'next/navigation';
import { useAuth } from '@/lib/auth/AuthContext';
import { useEffect, useState } from 'react';
import { DashboardLayout } from '@/components/layouts/DashboardLayout';
import { Loading } from '@/components/ui';

export default function ClientLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const { isAuthenticated, user, loading } = useAuth();
  const router = useRouter();
  const pathname = usePathname();
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    if (!loading && !isAuthenticated && mounted) {
      router.push(`/login?redirect=${encodeURIComponent(pathname)}`);
    } else if (!loading && isAuthenticated && user?.attributes?.['custom:role'] !== 'CLIENT') {
      const role = user?.attributes?.['custom:role']?.toLowerCase() || 'freelancer';
      router.push(`/${role}/dashboard`);
    }
  }, [isAuthenticated, loading, router, pathname, mounted, user]);

  if (!mounted || loading || (!isAuthenticated && mounted) || user?.attributes?.['custom:role'] !== 'CLIENT') {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <Loading size="lg" />
      </div>
    );
  }

  const breadcrumbs = [
    { label: 'Home', href: '/client/dashboard' },
  ];

  if (pathname !== '/client/dashboard') {
    const pathParts = pathname.split('/').filter(Boolean);
    if (pathParts.length > 1) {
      const currentPage = pathParts[pathParts.length - 1];
      breadcrumbs.push({
        label: currentPage.charAt(0).toUpperCase() + currentPage.slice(1).replace(/-/g, ' '),
        href: pathname,
      });
    }
  }

  return (
    <DashboardLayout>
      {children}
    </DashboardLayout>
  );
}
