'use client';

import { useState, useCallback, useEffect } from 'react';
import { Message, MessagingUser, Conversation } from './types';
import { MessageThread } from './components/MessageThread';
import { MessageInputBar } from './components/MessageInputBar';
import { ConversationList } from './components/ConversationList';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/Button';
import { Search, Menu, X } from 'lucide-react';
import Image from 'next/image';

export interface MessagingContainerProps {
  currentUser: MessagingUser;
  initialConversations?: Conversation[];
  onSendMessage: (conversationId: string, content: string) => Promise<void>;
  onLoadMoreMessages: (conversationId: string, before: Date) => Promise<Message[]>;
  onConversationSelect?: (conversationId: string) => void;
  onFileUpload?: (file: File) => Promise<string>;
  className?: string;
  showBackButton?: boolean;
  onBack?: () => void;
}

export function MessagingContainer({
  currentUser,
  initialConversations = [],
  onSendMessage,
  onLoadMoreMessages,
  onConversationSelect,
  onFileUpload,
  className,
  showBackButton = false,
  onBack,
}: MessagingContainerProps) {
  const [conversations, setConversations] = useState<Conversation[]>(initialConversations);
  const [selectedConversation, setSelectedConversation] = useState<Conversation | null>(null);
  const [messages, setMessages] = useState<Message[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isSending, setIsSending] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [isTyping, setIsTyping] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');

  // Handle conversation selection
  const handleSelectConversation = useCallback(async (conversationId: string) => {
    const conversation = conversations.find(c => c.id === conversationId);
    if (!conversation) return;

    setSelectedConversation(conversation);
    setIsLoading(true);
    
    try {
      const newMessages = await onLoadMoreMessages(conversationId, new Date());
      setMessages(newMessages);
      setHasMore(newMessages.length >= 20);
      
      setConversations(prev => 
        prev.map(c => c.id === conversationId ? { ...c, unreadCount: 0 } : c)
      );
      
      onConversationSelect?.(conversationId);
      setIsMobileMenuOpen(false);
    } catch (error) {
      console.error('Failed to load messages:', error);
    } finally {
      setIsLoading(false);
    }
  }, [conversations, onLoadMoreMessages, onConversationSelect]);

  const handleSendMessage = async (content: string) => {
    if (!selectedConversation || !content.trim()) return;
    
    const newMessage: Message = {
      id: `temp-${Date.now()}`,
      content,
      senderId: currentUser.id,
      timestamp: new Date(),
      status: 'sent',
      type: 'text',
    };

    setMessages(prev => [...prev, newMessage]);
    setIsSending(true);

    try {
      await onSendMessage(selectedConversation.id, content);
      
      setConversations(prev => 
        prev.map(conv => 
          conv.id === selectedConversation.id
            ? {
                ...conv,
                lastMessage: { ...newMessage },
                updatedAt: new Date(),
              }
            : conv
        )
      );
    } catch (error) {
      console.error('Failed to send message:', error);
    } finally {
      setIsSending(false);
    }
  };

  const handleLoadMore = useCallback(async () => {
    if (!selectedConversation || isLoading || !hasMore || messages.length === 0) return;
    
    setIsLoading(true);
    try {
      const oldestMessage = messages[0];
      const newMessages = await onLoadMoreMessages(
        selectedConversation.id,
        new Date(oldestMessage.timestamp)
      );
      
      setMessages(prev => [...newMessages, ...prev]);
      setHasMore(newMessages.length >= 20);
    } catch (error) {
      console.error('Failed to load more messages:', error);
    } finally {
      setIsLoading(false);
    }
  }, [selectedConversation, messages, isLoading, hasMore, onLoadMoreMessages]);

  useEffect(() => {
    const currentIds = conversations.map(c => c.id).sort().join(',');
    const newIds = initialConversations.map(c => c.id).sort().join(',');
    
    if (currentIds !== newIds) {
      setConversations(initialConversations);
    }
  }, [initialConversations, conversations]);

  useEffect(() => {
    if (conversations.length > 0 && !selectedConversation) {
      handleSelectConversation(conversations[0].id);
    }
  }, [conversations, selectedConversation, handleSelectConversation]);

  const otherUser = selectedConversation?.participants.find(
    p => p.id !== currentUser.id
  );

  const filteredConversations = conversations.filter(conversation => {
    if (!searchQuery) return true;
    const otherUser = conversation.participants.find(p => p.id !== currentUser.id);
    return otherUser?.name.toLowerCase().includes(searchQuery.toLowerCase());
  });

  return (
    <div className={cn('flex h-full bg-background', className)}>
      {/* Mobile menu button */}
      <div className="md:hidden fixed top-4 left-4 z-20">
        <Button
          variant="outline"
          size="icon"
          onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
          className="bg-background/80 backdrop-blur-sm"
        >
          {isMobileMenuOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
        </Button>
      </div>

      {/* Sidebar - Conversations */}
      <div
        className={cn(
          'absolute md:relative z-10 w-full max-w-xs bg-background border-r border-border h-full transition-transform duration-300 ease-in-out',
          'md:translate-x-0',
          isMobileMenuOpen ? 'translate-x-0' : '-translate-x-full',
          'md:block',
          'shadow-lg md:shadow-none'
        )}
      >
        <div className="p-4 border-b border-border">
          <h2 className="text-xl font-semibold">Messages</h2>
          <div className="mt-3 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <input
              type="text"
              placeholder="Search conversations..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-2 bg-muted/50 rounded-full text-sm focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2"
            />
          </div>
        </div>
        
        <ConversationList
          conversations={filteredConversations}
          selectedConversationId={selectedConversation?.id}
          onSelectConversation={handleSelectConversation}
          currentUserId={currentUser.id}
          loading={isLoading && messages.length === 0}
        />
      </div>

      {/* Main chat area */}
      <div className="flex-1 flex flex-col h-full">
        {selectedConversation ? (
          <>
            {/* Chat header */}
            <div className="border-b border-border p-4 flex items-center justify-between bg-background/80 backdrop-blur-sm sticky top-0 z-10">
              <div className="flex items-center space-x-3">
                {showBackButton && (
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={onBack}
                    className="md:hidden"
                  >
                    <X className="h-5 w-5" />
                  </Button>
                )}
                <div className="relative">
                  <div className="w-10 h-10 rounded-full bg-muted-foreground/10 flex items-center justify-center overflow-hidden">
                    {otherUser?.avatar ? (
                      <Image
                        src={otherUser.avatar}
                        alt={otherUser.name}
                        className="w-full h-full object-cover"
                        width={40}
                        height={40}
                        unoptimized={!otherUser.avatar.startsWith('/')}
                      />
                    ) : (
                      <span className="text-lg font-medium text-muted-foreground">
                        {otherUser?.name?.charAt(0).toUpperCase() || '?'}
                      </span>
                    )}
                  </div>
                  {otherUser?.isOnline && (
                    <span className="absolute bottom-0 right-0 w-3 h-3 rounded-full bg-green-500 border-2 border-background"></span>
                  )}
                </div>
                <div>
                  <h3 className="font-medium">{otherUser?.name || 'Unknown User'}</h3>
                  <p className="text-xs text-muted-foreground">
                    {otherUser?.isOnline ? 'Online' : 'Offline'}
                  </p>
                </div>
              </div>
            </div>

            {/* Message thread */}
            <MessageThread
              messages={messages}
              currentUser={currentUser}
              otherUser={otherUser!}
              loading={isLoading}
              onLoadMore={handleLoadMore}
              hasMore={hasMore}
              isTyping={isTyping}
              className="flex-1"
            />

            {/* Message input */}
            <MessageInputBar
              onSend={handleSendMessage}
              onTyping={setIsTyping}
              disabled={isSending}
              onFileUpload={onFileUpload}
            />
          </>
        ) : (
          <div className="flex-1 flex items-center justify-center text-muted-foreground">
            <p>Select a conversation to start messaging</p>
          </div>
        )}
      </div>
    </div>
  );
}
