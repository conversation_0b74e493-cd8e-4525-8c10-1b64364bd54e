'use client';

import { useState, useCallback, useEffect } from 'react';
import { Message, MessagingUser, Conversation } from './types';
import { MessageList } from './components/MessageList';
import { MessageInput } from './components/MessageInput';
import { ConversationList } from './components/ConversationList';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/Button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Badge } from '@/components/ui/Badge';
import { Avatar, AvatarImage, AvatarFallback } from '@/components/ui/Avatar';
import { Search, Menu, X, ArrowLeft } from 'lucide-react';

export interface MessagingPageProps {
  currentUser: MessagingUser;
  initialConversations?: Conversation[];
  onSendMessage: (conversationId: string, content: string) => Promise<void>;
  onLoadMoreMessages: (conversationId: string, before: Date) => Promise<Message[]>;
  onFileUpload?: (file: File) => Promise<string>;
  className?: string;
}

export function MessagingPage({
  currentUser,
  initialConversations = [],
  onSendMessage,
  onLoadMoreMessages,
  onFileUpload,
  className,
}: MessagingPageProps) {
  const [conversations, setConversations] = useState<Conversation[]>(initialConversations);
  const [selectedConversation, setSelectedConversation] = useState<Conversation | null>(null);
  const [messages, setMessages] = useState<Message[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isSending, setIsSending] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  // Mock messages for demonstration
  const mockMessages: Message[] = [
    {
      id: '1',
      content: 'Hello! I saw your profile and would like to discuss a project with you.',
      senderId: selectedConversation?.participants.find(p => p.id !== currentUser.id)?.id || 'other',
      timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
      status: 'read',
      type: 'text',
    },
    {
      id: '2',
      content: 'Hi there! I\'d be happy to discuss your project. What kind of work are you looking for?',
      senderId: currentUser.id,
      timestamp: new Date(Date.now() - 1.5 * 60 * 60 * 1000), // 1.5 hours ago
      status: 'read',
      type: 'text',
    },
    {
      id: '3',
      content: 'I need a web application built with React and Node.js. The project involves creating a dashboard for managing customer data.',
      senderId: selectedConversation?.participants.find(p => p.id !== currentUser.id)?.id || 'other',
      timestamp: new Date(Date.now() - 1 * 60 * 60 * 1000), // 1 hour ago
      status: 'read',
      type: 'text',
    },
    {
      id: '4',
      content: 'That sounds like a great project! I have extensive experience with React and Node.js. Could you share more details about the specific requirements?',
      senderId: currentUser.id,
      timestamp: new Date(Date.now() - 30 * 60 * 1000), // 30 minutes ago
      status: 'delivered',
      type: 'text',
    },
  ];

  useEffect(() => {
    if (selectedConversation) {
      setMessages(mockMessages);
    }
  }, [selectedConversation]);

  const handleSelectConversation = useCallback((conversationId: string) => {
    const conversation = conversations.find(c => c.id === conversationId);
    if (conversation) {
      setSelectedConversation(conversation);
      setIsMobileMenuOpen(false);
    }
  }, [conversations]);

  const handleSendMessage = async (content: string) => {
    if (!selectedConversation || !content.trim()) return;
    
    const newMessage: Message = {
      id: `temp-${Date.now()}`,
      content,
      senderId: currentUser.id,
      timestamp: new Date(),
      status: 'sent',
      type: 'text',
    };

    setMessages(prev => [...prev, newMessage]);
    setIsSending(true);

    try {
      await onSendMessage(selectedConversation.id, content);
      
      // Update conversation's last message
      setConversations(prev => 
        prev.map(conv => 
          conv.id === selectedConversation.id
            ? {
                ...conv,
                lastMessage: { ...newMessage },
                updatedAt: new Date(),
              }
            : conv
        )
      );
    } catch (error) {
      console.error('Failed to send message:', error);
      // Remove the temporary message on error
      setMessages(prev => prev.filter(m => m.id !== newMessage.id));
    } finally {
      setIsSending(false);
    }
  };

  const handleFileUpload = async (file: File) => {
    if (!onFileUpload) return;
    
    try {
      const fileUrl = await onFileUpload(file);
      
      const fileMessage: Message = {
        id: `file-${Date.now()}`,
        content: `Shared a file: ${file.name}`,
        senderId: currentUser.id,
        timestamp: new Date(),
        status: 'sent',
        type: 'file',
        fileInfo: {
          name: file.name,
          type: file.type,
          size: file.size,
          url: fileUrl,
        },
      };

      setMessages(prev => [...prev, fileMessage]);
    } catch (error) {
      console.error('Failed to upload file:', error);
    }
  };

  const handleLoadMore = () => {
    if (!selectedConversation || isLoading) return;
    
    setIsLoading(true);
    // Simulate loading more messages
    setTimeout(() => {
      setIsLoading(false);
      setHasMore(false); // For demo purposes
    }, 1000);
  };

  const filteredConversations = conversations.filter(conv =>
    conv.participants.some(p => 
      p.id !== currentUser.id && 
      p.name.toLowerCase().includes(searchQuery.toLowerCase())
    )
  );

  const otherUser = selectedConversation?.participants.find(p => p.id !== currentUser.id);

  return (
    <div className={cn('h-full flex bg-background border rounded-lg overflow-hidden', className)}>
      {/* Conversations Sidebar */}
      <div className={cn(
        'w-80 border-r border-border flex flex-col bg-background',
        'lg:flex',
        isMobileMenuOpen ? 'flex absolute inset-0 z-50 lg:relative lg:inset-auto' : 'hidden lg:flex'
      )}>
        {/* Header */}
        <div className="p-4 border-b border-border">
          <div className="flex items-center justify-between mb-3">
            <h2 className="text-xl font-semibold">Messages</h2>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setIsMobileMenuOpen(false)}
              className="lg:hidden"
            >
              <X className="h-5 w-5" />
            </Button>
          </div>
          
          {/* Search */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <input
              type="text"
              placeholder="Search conversations..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-2 bg-muted/50 rounded-full text-sm focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2"
            />
          </div>
        </div>
        
        {/* Conversations List */}
        <ConversationList
          conversations={filteredConversations}
          selectedConversationId={selectedConversation?.id}
          onSelectConversation={handleSelectConversation}
          currentUserId={currentUser.id}
          loading={isLoading && messages.length === 0}
        />
      </div>

      {/* Main Chat Area */}
      <div className="flex-1 flex flex-col">
        {selectedConversation && otherUser ? (
          <>
            {/* Chat Header */}
            <div className="p-4 border-b border-border bg-background">
              <div className="flex items-center gap-3">
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => setIsMobileMenuOpen(true)}
                  className="lg:hidden"
                >
                  <ArrowLeft className="h-5 w-5" />
                </Button>
                
                <Avatar className="w-10 h-10">
                  <AvatarImage src={otherUser.avatar} alt={otherUser.name} />
                  <AvatarFallback>
                    {otherUser.name.charAt(0).toUpperCase()}
                  </AvatarFallback>
                </Avatar>
                
                <div className="flex-1">
                  <div className="flex items-center gap-2">
                    <h3 className="font-medium">{otherUser.name}</h3>
                    <Badge variant={otherUser.role === 'CLIENT' ? 'default' : 'secondary'}>
                      {otherUser.role}
                    </Badge>
                  </div>
                  <p className="text-xs text-muted-foreground">
                    {otherUser.isOnline ? 'Online' : 'Offline'}
                  </p>
                </div>
              </div>
            </div>

            {/* Messages */}
            <MessageList
              messages={messages}
              currentUser={currentUser}
              otherUser={otherUser}
              loading={isLoading}
              onLoadMore={handleLoadMore}
              hasMore={hasMore}
              className="flex-1"
            />

            {/* Message Input */}
            <MessageInput
              onSend={handleSendMessage}
              onFileUpload={handleFileUpload}
              disabled={isSending}
              placeholder={`Message ${otherUser.name}...`}
            />
          </>
        ) : (
          <div className="flex-1 flex items-center justify-center bg-muted/50">
            <div className="text-center p-6 max-w-md">
              <Button
                variant="ghost"
                size="icon"
                onClick={() => setIsMobileMenuOpen(true)}
                className="lg:hidden mb-4"
              >
                <Menu className="h-5 w-5" />
              </Button>
              <h2 className="text-2xl font-bold mb-2">Your Messages</h2>
              <p className="text-muted-foreground mb-6">
                Select a conversation or start a new one to begin messaging.
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
