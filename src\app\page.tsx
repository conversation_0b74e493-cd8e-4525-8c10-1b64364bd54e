"use client";
import { useEffect } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "@/lib/auth/AuthContext";
import { Loading } from "@/components/ui";

export default function Home() {
  const { user, loading, isAuthenticated } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!loading) {
      if (isAuthenticated && user) {
        const userRole = user.attributes?.['custom:role'] as string || 'CLIENT';
        const redirectPath = userRole === 'CLIENT' ? '/client/dashboard' : 
                           userRole === 'FREELANCER' ? '/freelancer/dashboard' :
                           userRole === 'ADMIN' ? '/admin/dashboard' : '/';
        router.replace(redirectPath);
      } else if (!isAuthenticated) {
        router.replace('/login');
      }
    }
  }, [user, isAuthenticated, loading, router]);

  return (
    <div className="min-h-screen bg-background flex items-center justify-center">
      <div className="text-center space-y-4">
        <Loading size="lg" />
        <p className="text-muted-foreground">
          {loading ? 'Loading...' : 'Redirecting...'}
        </p>
      </div>
    </div>
  );
}
