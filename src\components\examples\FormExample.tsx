import React, { useState } from 'react';
import { 
  Form, 
  FormField, 
  Input, 
  Textarea, 
  Select, 
  Checkbox, 
  RadioGroup, 
  Button, 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle 
} from '@/components/ui';
import { Container } from '@/components/layout';

const skillOptions = [
  { value: 'javascript', label: 'JavaScript' },
  { value: 'python', label: 'Python' },
  { value: 'react', label: 'React' },
  { value: 'nodejs', label: 'Node.js' },
  { value: 'design', label: 'UI/UX Design' },
];

const experienceOptions = [
  { value: 'beginner', label: 'Beginner (0-1 years)', description: 'Just starting out in your field' },
  { value: 'intermediate', label: 'Intermediate (2-5 years)', description: 'Some experience under your belt' },
  { value: 'expert', label: 'Expert (5+ years)', description: 'Highly experienced professional' },
];

const FormExample: React.FC = () => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    bio: '',
    primarySkill: '',
    experience: '',
    availableForWork: false,
    newsletter: false,
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    // Simple validation
    const newErrors: Record<string, string> = {};
    
    if (!formData.name.trim()) {
      newErrors.name = 'Name is required';
    }
    
    if (!formData.email.trim()) {
      newErrors.email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'Email is invalid';
    }
    
    if (!formData.bio.trim()) {
      newErrors.bio = 'Bio is required';
    }
    
    if (!formData.primarySkill) {
      newErrors.primarySkill = 'Please select a primary skill';
    }
    
    if (!formData.experience) {
      newErrors.experience = 'Please select your experience level';
    }
    
    setErrors(newErrors);
    
    if (Object.keys(newErrors).length === 0) {
      alert('Form submitted successfully!');
    }
  };

  const handleInputChange = (field: string, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  return (
    <div className="min-h-screen bg-background py-12">
      <Container size="md">
        <Card>
          <CardHeader>
            <CardTitle>Freelancer Profile Setup</CardTitle>
            <CardDescription>
              Complete your profile to start receiving project invitations
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Form onSubmit={handleSubmit}>
              {/* Basic Information */}
              <div className="space-y-6">
                <div className="grid gap-6 md:grid-cols-2">
                  <FormField
                    label="Full Name"
                    required
                    error={errors.name}
                  >
                    <Input
                      placeholder="Enter your full name"
                      value={formData.name}
                      onChange={(e) => handleInputChange('name', e.target.value)}
                      error={!!errors.name}
                    />
                  </FormField>

                  <FormField
                    label="Email Address"
                    required
                    error={errors.email}
                  >
                    <Input
                      type="email"
                      placeholder="Enter your email"
                      value={formData.email}
                      onChange={(e) => handleInputChange('email', e.target.value)}
                      error={!!errors.email}
                    />
                  </FormField>
                </div>

                <FormField
                  label="Professional Bio"
                  required
                  error={errors.bio}
                  hint="Tell potential clients about your skills and experience"
                >
                  <Textarea
                    placeholder="Describe your professional background, skills, and what makes you unique..."
                    value={formData.bio}
                    onChange={(e) => handleInputChange('bio', e.target.value)}
                    error={!!errors.bio}
                    rows={4}
                  />
                </FormField>

                <FormField
                  label="Primary Skill"
                  required
                  error={errors.primarySkill}
                >
                  <Select
                    options={skillOptions}
                    placeholder="Select your primary skill"
                    value={formData.primarySkill}
                    onChange={(e) => handleInputChange('primarySkill', e.target.value)}
                    error={!!errors.primarySkill}
                  />
                </FormField>

                <FormField
                  label="Experience Level"
                  required
                  error={errors.experience}
                >
                  <RadioGroup
                    name="experience"
                    options={experienceOptions}
                    value={formData.experience}
                    onChange={(value) => handleInputChange('experience', value)}
                    error={!!errors.experience}
                  />
                </FormField>

                {/* Preferences */}
                <div className="space-y-4">
                  <h3 className="text-lg font-medium">Preferences</h3>
                  
                  <Checkbox
                    label="Available for new projects"
                    description="Let clients know you're currently accepting new work"
                    checked={formData.availableForWork}
                    onChange={(e) => handleInputChange('availableForWork', e.target.checked)}
                  />

                  <Checkbox
                    label="Subscribe to newsletter"
                    description="Receive updates about new features and opportunities"
                    checked={formData.newsletter}
                    onChange={(e) => handleInputChange('newsletter', e.target.checked)}
                  />
                </div>

                {/* Submit Button */}
                <div className="flex justify-end space-x-4">
                  <Button type="button" variant="outline">
                    Save as Draft
                  </Button>
                  <Button type="submit">
                    Complete Profile
                  </Button>
                </div>
              </div>
            </Form>
          </CardContent>
        </Card>

        {/* Form Data Preview */}
        <Card className="mt-8">
          <CardHeader>
            <CardTitle>Form Data Preview</CardTitle>
            <CardDescription>
              This shows the current form state (for development purposes)
            </CardDescription>
          </CardHeader>
          <CardContent>
            <pre className="text-sm bg-muted p-4 rounded-md overflow-auto">
              {JSON.stringify(formData, null, 2)}
            </pre>
          </CardContent>
        </Card>
      </Container>
    </div>
  );
};

export default FormExample;
