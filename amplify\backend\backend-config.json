{"api": {"authApi": {"dependsOn": [{"attributes": ["Name", "<PERSON><PERSON>"], "category": "function", "resourceName": "cognitoLogin"}, {"attributes": ["Name", "<PERSON><PERSON>"], "category": "function", "resourceName": "verifyUser"}, {"attributes": ["Name", "<PERSON><PERSON>"], "category": "function", "resourceName": "cognitoSignup"}], "providerPlugin": "awscloudformation", "service": "API Gateway"}, "myvillagefreelance": {"dependsOn": [{"attributes": ["UserPoolId"], "category": "auth", "resourceName": "myvillagefreelanceb6232a5f"}], "output": {"authConfig": {"additionalAuthenticationProviders": [{"authenticationType": "AMAZON_COGNITO_USER_POOLS", "userPoolConfig": {"userPoolId": "authmyvillagefreelanceb6232a5f"}}], "defaultAuthentication": {"apiKeyConfig": {"apiKeyExpirationDays": 7}, "authenticationType": "API_KEY"}}}, "providerPlugin": "awscloudformation", "service": "AppSync"}}, "auth": {"myvillagefreelanceb6232a5f": {"customAuth": false, "dependsOn": [], "frontendAuthConfig": {"mfaConfiguration": "OFF", "mfaTypes": ["SMS"], "passwordProtectionSettings": {"passwordPolicyCharacters": [], "passwordPolicyMinLength": 8}, "signupAttributes": ["EMAIL"], "socialProviders": [], "usernameAttributes": [], "verificationMechanisms": ["EMAIL"]}, "providerPlugin": "awscloudformation", "service": "Cognito"}}, "function": {"cognitoLogin": {"build": true, "dependsOn": [{"attributes": ["UserPoolId"], "category": "auth", "resourceName": "myvillagefreelanceb6232a5f"}], "providerPlugin": "awscloudformation", "service": "Lambda"}, "cognitoSignup": {"build": true, "dependsOn": [{"attributes": ["UserPoolId"], "category": "auth", "resourceName": "myvillagefreelanceb6232a5f"}], "providerPlugin": "awscloudformation", "service": "Lambda"}, "verifyUser": {"build": true, "dependsOn": [{"attributes": ["UserPoolId"], "category": "auth", "resourceName": "myvillagefreelanceb6232a5f"}], "providerPlugin": "awscloudformation", "service": "Lambda"}}, "parameters": {"AMPLIFY_function_cognitoLogin_deploymentBucketName": {"usedBy": [{"category": "function", "resourceName": "cognitoLogin"}]}, "AMPLIFY_function_cognitoLogin_s3Key": {"usedBy": [{"category": "function", "resourceName": "cognitoLogin"}]}, "AMPLIFY_function_cognitoLogin_userPoolClientId": {"usedBy": [{"category": "function", "resourceName": "cognitoLogin"}]}, "AMPLIFY_function_cognitoSignup_authMyvillagefreelanceb6232A5FUserpoolwebclientid": {"usedBy": [{"category": "function", "resourceName": "cognitoSignup"}]}, "AMPLIFY_function_cognitoSignup_autoConfirm": {"usedBy": [{"category": "function", "resourceName": "cognitoSignup"}]}, "AMPLIFY_function_cognitoSignup_defaultUserGroup": {"usedBy": [{"category": "function", "resourceName": "cognitoSignup"}]}, "AMPLIFY_function_cognitoSignup_deploymentBucketName": {"usedBy": [{"category": "function", "resourceName": "cognitoSignup"}]}, "AMPLIFY_function_cognitoSignup_s3Key": {"usedBy": [{"category": "function", "resourceName": "cognitoSignup"}]}, "AMPLIFY_function_verifyUser_authMyvillagefreelanceb6232A5FUserpoolwebclientid": {"usedBy": [{"category": "function", "resourceName": "verifyUser"}]}, "AMPLIFY_function_verifyUser_deploymentBucketName": {"usedBy": [{"category": "function", "resourceName": "verifyUser"}]}, "AMPLIFY_function_verifyUser_s3Key": {"usedBy": [{"category": "function", "resourceName": "verifyUser"}]}, "AMPLIFY_function_verifyUser_userPoolId": {"usedBy": [{"category": "function", "resourceName": "verifyUser"}]}}}