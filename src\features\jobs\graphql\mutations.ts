import { gql } from '@apollo/client';

export const CREATE_JOB = gql`
  mutation CreateJob($input: CreateJobInput!) {
    createJob(input: $input) {
      id
      title
      description
      budget
      category
      deadline
      clientId
      createdAt
      updatedAt
    }
  }
`;

export const UPDATE_JOB = gql`
  mutation UpdateJob($input: UpdateJobInput!) {
    updateJob(input: $input) {
      id
      title
      description
      budget
      category
      deadline
      updatedAt
    }
  }
`;

export const DELETE_JOB = gql`
  mutation DeleteJob($input: DeleteJobInput!) {
    deleteJob(input: $input) {
      id
    }
  }
`;

export const SUBMIT_PROPOSAL = gql`
  mutation SubmitProposal($input: CreateJobProposalInput!) {
    submitProposal(input: $input) {
      id
      jobId
      freelancerId
      coverLetter
      proposedRate
      status
      createdAt
      updatedAt
    }
  }
`;

export const UPDATE_PROPOSAL_STATUS = gql`
  mutation UpdateProposalStatus($input: UpdateProposalInput!) {
    updateProposal(input: $input) {
      id
      status
      updatedAt
    }
  }
`;

export const WITHDRAW_PROPOSAL = gql`
  mutation WithdrawProposal($id: ID!) {
    withdrawProposal(id: $id) {
      id
      status
      updatedAt
    }
  }
`;
