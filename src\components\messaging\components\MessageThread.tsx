'use client';

import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { Message, MessagingUser } from '../types';
import { MessageBubble } from './MessageBubble';
import { cn } from '@/lib/utils';
import { Skeleton } from '@/components/ui/Skeleton';

export interface MessageThreadProps {
  messages: Message[];
  currentUser: MessagingUser;
  otherUser: MessagingUser;
  loading?: boolean;
  onLoadMore: () => void;
  hasMore: boolean;
  isTyping: boolean;
  className?: string;
}

export function MessageThread({
  messages,
  currentUser,
  otherUser,
  loading = false,
  onLoadMore,
  hasMore,
  isTyping,
  className,
}: MessageThreadProps) {
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const [isInitialLoad, setIsInitialLoad] = useState(true);
  const prevMessagesLengthRef = useRef(messages.length);
  const scrollPositionRef = useRef<number | null>(null);
  
  const isNewMessageFromOther = 
    messages.length > 0 && 
    messages[messages.length - 1]?.senderId !== currentUser.id &&
    messages.length !== prevMessagesLengthRef.current;
    
  // Update the ref when messages change
  useEffect(() => {
    prevMessagesLengthRef.current = messages.length;
  }, [messages.length]);
  
  const isNearBottom = useCallback((threshold = 100) => {
    if (!containerRef.current) return true;
    const { scrollTop, scrollHeight, clientHeight } = containerRef.current;
    return scrollHeight - scrollTop - clientHeight < threshold;
  }, []);

  const isBottom = useMemo(() => isNearBottom(), [isNearBottom]);

  useEffect(() => {
    if (isInitialLoad && messages.length > 0) {
      scrollToBottom('auto');
      setIsInitialLoad(false);
      return;
    }

    if (isBottom || isNewMessageFromOther) {
      scrollToBottom('smooth');
    }
  }, [messages.length, isInitialLoad, isNewMessageFromOther, isBottom, currentUser.id, isNearBottom]);

  const scrollToBottom = (behavior: ScrollBehavior = 'smooth') => {
    messagesEndRef.current?.scrollIntoView({ behavior });
  };

  const handleScroll = () => {
    if (!containerRef.current) return;
    
    const { scrollTop, scrollHeight } = containerRef.current;
    
    if (scrollTop < 200 && hasMore && !loading) {
      scrollPositionRef.current = scrollHeight;
      onLoadMore();
    }
  };

  useEffect(() => {
    if (scrollPositionRef.current !== null && containerRef.current) {
      containerRef.current.scrollTop = containerRef.current.scrollHeight - scrollPositionRef.current;
      scrollPositionRef.current = null;
    }
  }, [messages.length]);

  const groupedMessages = messages.reduce<Record<string, Message[]>>((groups, message) => {
    const date = new Date(message.timestamp).toDateString();
    if (!groups[date]) {
      groups[date] = [];
    }
    groups[date].push(message);
    return groups;
  }, {});

  const formatDateHeader = (dateString: string) => {
    const today = new Date();
    const date = new Date(dateString);
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    if (date.toDateString() === today.toDateString()) {
      return 'Today';
    } else if (date.toDateString() === yesterday.toDateString()) {
      return 'Yesterday';
    } else {
      return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' });
    }
  };

  return (
    <div 
      ref={containerRef}
      onScroll={handleScroll}
      className={cn(
        'flex-1 overflow-y-auto p-4 space-y-6',
        'scrollbar-thin scrollbar-thumb-muted-foreground/20 scrollbar-track-transparent',
        className
      )}
    >
      {loading && messages.length === 0 ? (
        <div className="space-y-4">
          {[...Array(5)].map((_, i) => (
            <div 
              key={i} 
              className={cn(
                'flex',
                i % 2 === 0 ? 'justify-start' : 'justify-end',
                'animate-pulse'
              )}
            >
              <div className={cn(
                'max-w-[80%] md:max-w-[70%] lg:max-w-[60%]',
                i % 2 === 0 ? 'items-start' : 'items-end'
              )}>
                <Skeleton className="h-20 w-full rounded-2xl" />
                <Skeleton className="h-4 w-24 mt-1 rounded-full" />
              </div>
            </div>
          ))}
        </div>
      ) : (
        <>
          {hasMore && (
            <div className="flex justify-center py-2">
              <button
                onClick={() => onLoadMore()}
                disabled={loading}
                className="text-sm text-muted-foreground hover:text-foreground transition-colors"
              >
                {loading ? 'Loading...' : 'Load older messages'}
              </button>
            </div>
          )}
          
          {Object.entries(groupedMessages).map(([date, dateMessages]) => (
            <div key={date} className="space-y-6">
              <div className="sticky top-2 z-10">
                <div className="relative">
                  <div className="absolute inset-0 flex items-center">
                    <div className="w-full border-t border-border" />
                  </div>
                  <div className="relative flex justify-center">
                    <span className="px-3 bg-background text-xs text-muted-foreground rounded-full border border-border">
                      {formatDateHeader(date)}
                    </span>
                  </div>
                </div>
              </div>
              
              <div className="space-y-4">
                {dateMessages.map((message, index) => {
                  const isCurrentUser = message.senderId === currentUser.id;
                  const showAvatar = index === 0 || 
                    dateMessages[index - 1]?.senderId !== message.senderId;
                  const showTimestamp = index === dateMessages.length - 1 || 
                    dateMessages[index + 1]?.senderId !== message.senderId ||
                    (new Date(dateMessages[index + 1]?.timestamp).getTime() - 
                     new Date(message.timestamp).getTime()) > 5 * 60 * 1000; // 5 minutes
                  
                  return (
                    <MessageBubble
                      key={message.id}
                      message={message}
                      isCurrentUser={isCurrentUser}
                      showAvatar={showAvatar}
                      showTimestamp={showTimestamp}
                      user={isCurrentUser ? currentUser : otherUser}
                    />
                  );
                })}
              </div>
            </div>
          ))}
          
          {isTyping && (
            <div className="flex items-center space-x-2 p-2">
              <div className="w-8 h-8 rounded-full bg-muted flex items-center justify-center">
                <span className="text-muted-foreground">
                  {otherUser.name.charAt(0).toUpperCase()}
                </span>
              </div>
              <div className="flex space-x-1">
                <div className="w-2 h-2 rounded-full bg-muted-foreground animate-bounce" style={{ animationDelay: '0ms' }} />
                <div className="w-2 h-2 rounded-full bg-muted-foreground animate-bounce" style={{ animationDelay: '150ms' }} />
                <div className="w-2 h-2 rounded-full bg-muted-foreground animate-bounce" style={{ animationDelay: '300ms' }} />
              </div>
            </div>
          )}
          
          <div ref={messagesEndRef} />
        </>
      )}
    </div>
  );
}
