"use client";
import React, { useEffect, useState } from "react";
import { DashboardLayout } from "@/components/layouts/DashboardLayout";
import ProfilePhotoUploader from "@/components/ProfilePhotoUploader";
import { useAuth } from "@/lib/auth/AuthContext";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Textarea } from '@/components/ui/Textarea';
import { Label } from '@/components/ui/Form';
import { Icon } from '@/components/ui/Icon';

export default function ProfilePage() {
  const { user, updateProfile, cognitoUserId, refresh, isAuthenticated } = useAuth();
  const [name, setName] = useState("");
  const [bio, setBio] = useState("");
  const [skills, setSkills] = useState("");
  const [message, setMessage] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    if (user) {
      setName(user.name || "");
      setBio(user.bio || "");
      setSkills((user.skills || []).join(", "));
    }
    setIsLoading(false);
  }, [user]);

  if (isLoading || !isAuthenticated || !user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Icon name="Loader2" size="xl" className="animate-spin text-blue-500" />
      </div>
    );
  }



  const onSave = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    try {
      await updateProfile({ name, bio, skills: skills.split(",").map((s) => s.trim()).filter(Boolean) });
      setMessage("Saved.");
      await refresh();
    } catch (e) {
      const errorMessage = e instanceof Error ? e.message : 'An unknown error occurred';
      setError(errorMessage);
    }
  };

  const userRole = user.attributes?.['custom:role'] || 'CLIENT';
  const breadcrumbs = [
    { label: 'Home', href: `/${userRole.toLowerCase()}/dashboard` },
    { label: 'Profile', href: '/profile' },
  ];

  return (
    <DashboardLayout
      breadcrumbs={breadcrumbs}
      title="Profile"
      description="Manage your profile information and settings"
    >
      <div className="w-full max-w-4xl mx-auto px-4 sm:px-6 py-8">
        <div className="space-y-6">
          <div className="text-center">
            <h1 className="text-3xl font-bold tracking-tight text-foreground">Your Profile</h1>
            <p className="mt-2 text-muted-foreground">
              Update your personal information and profile settings
            </p>
          </div>
          
          {error && (
            <div className="rounded-md bg-red-50 p-4">
              <div className="flex">
                <div className="flex-shrink-0">
                  <Icon name="XCircle" size="md" className="text-red-400" />
                </div>
                <div className="ml-3">
                  <p className="text-sm font-medium text-red-800">{error}</p>
                </div>
              </div>
            </div>
          )}

          <div className="space-y-6">
            <Card className="bg-card rounded-lg shadow-sm border">
              <CardHeader>
                <CardTitle className="text-lg font-medium">Profile Photo</CardTitle>
                <CardDescription className="text-muted-foreground">
                  Upload and manage your profile picture
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ProfilePhotoUploader />
              </CardContent>
            </Card>

            <Card className="bg-card rounded-lg shadow-sm border">
              <CardHeader>
                <CardTitle className="text-lg font-medium">Personal Information</CardTitle>
                <CardDescription className="text-muted-foreground">
                  Update your personal details
                </CardDescription>
              </CardHeader>
              <CardContent>
                <form onSubmit={onSave} className="space-y-6">
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="name" className="text-sm font-medium">Full Name</Label>
                      <Input
                        id="name"
                        value={name}
                        onChange={(e) => setName(e.target.value)}
                        placeholder="Enter your full name"
                        className="w-full"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="bio" className="text-sm font-medium">About You</Label>
                      <Textarea
                        id="bio"
                        value={bio}
                        onChange={(e) => setBio(e.target.value)}
                        placeholder="Tell us about yourself, your experience, and your skills"
                        rows={4}
                        className="min-h-[100px]"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="skills" className="text-sm font-medium">Skills</Label>
                      <Input
                        id="skills"
                        value={skills}
                        onChange={(e) => setSkills(e.target.value)}
                        placeholder="e.g., Web Development, Design, Marketing, Writing"
                        className="w-full"
                      />
                      <p className="text-xs text-muted-foreground mt-1">
                        Separate skills with commas
                      </p>
                    </div>
                  </div>

                  {message && (
                    <div className="rounded-md bg-green-50 p-4">
                      <div className="flex">
                        <div className="flex-shrink-0">
                          <Icon name="CheckCircle2" size="md" className="text-green-400" />
                        </div>
                        <div className="ml-3">
                          <p className="text-sm font-medium text-green-800">{message}</p>
                        </div>
                      </div>
                    </div>
                  )}

                  <div className="flex justify-end">
                    <Button size="sm" type="submit" className="w-full sm:w-auto">
                      Save Changes
                    </Button>
                  </div>
                </form>
              </CardContent>
            </Card>

            {cognitoUserId && (
              <Card className="bg-card rounded-lg shadow-sm border">
                <CardHeader>
                  <CardTitle className="text-lg font-medium">Account Information</CardTitle>
                  <CardDescription className="text-muted-foreground">
                    Your account details
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between py-2">
                      <span className="text-sm font-medium text-muted-foreground">User ID</span>
                      <span className="text-sm font-mono">{cognitoUserId}</span>
                    </div>
                    <div className="flex items-center justify-between py-2">
                      <span className="text-sm font-medium text-muted-foreground">Email</span>
                      <span className="text-sm">{user?.attributes?.email || 'Not available'}</span>
                    </div>
                    <div className="flex items-center justify-between py-2">
                      <span className="text-sm font-medium text-muted-foreground">Account Type</span>
                      <span className="text-sm capitalize">{userRole.toLowerCase()}</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}

