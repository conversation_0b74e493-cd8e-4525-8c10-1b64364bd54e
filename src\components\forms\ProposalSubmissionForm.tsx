"use client";

import React, { useState } from "react";
import { Button } from "@/components/ui/Button";
import { Input } from "@/components/ui/Input";
import { Textarea } from "@/components/ui/Textarea";
import { Icon } from "@/components/ui/Icon";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
} from "@/components/ui/Card";
import { CreateJobProposalInput } from "@/types/proposal";

interface ProposalSubmissionFormProps {
  jobId: string;
  jobBudget?: number;
  onSubmit: (proposal: CreateJobProposalInput) => Promise<void>;
  onCancel?: () => void;
  isSubmitting?: boolean;
  className?: string;
}

interface FormData {
  coverLetter: string;
  bidAmount: string;
  proposedRate: string;
}

interface FormErrors {
  coverLetter?: string;
  bidAmount?: string;
  proposedRate?: string;
}

export const ProposalSubmissionForm: React.FC<ProposalSubmissionFormProps> = ({
  jobId,
  jobBudget,
  onSubmit,
  onCancel,
  isSubmitting = false,
  className = "",
}) => {
  const [formData, setFormData] = useState<FormData>({
    coverLetter: "",
    bidAmount: "",
    proposedRate: "",
  });

  const [errors, setErrors] = useState<FormErrors>({});

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    // Cover letter validation
    if (!formData.coverLetter.trim()) {
      newErrors.coverLetter = "Cover letter is required";
    } else if (formData.coverLetter.trim().length < 50) {
      newErrors.coverLetter = "Cover letter must be at least 50 characters";
    } else if (formData.coverLetter.trim().length > 2000) {
      newErrors.coverLetter = "Cover letter must be less than 2000 characters";
    }

    // Bid amount validation
    if (!formData.bidAmount.trim()) {
      newErrors.bidAmount = "Bid amount is required";
    } else {
      const bidAmount = parseFloat(formData.bidAmount);
      if (isNaN(bidAmount) || bidAmount <= 0) {
        newErrors.bidAmount = "Bid amount must be a positive number";
      } else if (bidAmount > 1000000) {
        newErrors.bidAmount = "Bid amount cannot exceed $1,000,000";
      }
    }

    // Proposed rate validation (optional)
    if (formData.proposedRate.trim()) {
      const proposedRate = parseFloat(formData.proposedRate);
      if (isNaN(proposedRate) || proposedRate <= 0) {
        newErrors.proposedRate = "Proposed rate must be a positive number";
      } else if (proposedRate > 10000) {
        newErrors.proposedRate = "Proposed rate cannot exceed $10,000/hour";
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (field: keyof FormData, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors((prev) => ({ ...prev, [field]: undefined }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    const proposalData: CreateJobProposalInput = {
      jobId,
      coverLetter: formData.coverLetter.trim(),
      bidAmount: parseFloat(formData.bidAmount),
      proposedRate: formData.proposedRate.trim()
        ? parseFloat(formData.proposedRate)
        : undefined,
    };

    await onSubmit(proposalData);
  };

  const handleCancel = () => {
    setFormData({
      coverLetter: "",
      bidAmount: "",
      proposedRate: "",
    });
    setErrors({});
    onCancel?.();
  };

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center">
          <Icon name="Send" size="sm" className="mr-2" />
          Submit Your Proposal
        </CardTitle>
        <CardDescription>
          Tell the client why you&#39;re the perfect fit for this job and what
          you&#39;ll charge.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Cover Letter */}
          <div className="space-y-2">
            <label
              htmlFor="coverLetter"
              className="block text-sm font-medium text-foreground"
            >
              Cover Letter <span className="text-destructive">*</span>
            </label>
            <Textarea
              id="coverLetter"
              placeholder="Explain why you're the best candidate for this job. Include relevant experience, skills, and how you plan to approach the project..."
              value={formData.coverLetter}
              onChange={(e) => handleInputChange("coverLetter", e.target.value)}
              rows={6}
              className={errors.coverLetter ? "border-destructive" : ""}
              disabled={isSubmitting}
            />
            {errors.coverLetter && (
              <p className="text-sm text-destructive">{errors.coverLetter}</p>
            )}
            <p className="text-xs text-muted-foreground">
              {formData.coverLetter.length}/2000 characters (minimum 50)
            </p>
          </div>

          {/* Bid Amount */}
          <div className="space-y-2">
            <label
              htmlFor="bidAmount"
              className="block text-sm font-medium text-foreground"
            >
              Your Bid Amount <span className="text-destructive">*</span>
            </label>
            <div className="relative">
              <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                <span className="text-muted-foreground text-sm">$</span>
              </div>
              <Input
                id="bidAmount"
                type="number"
                min="0"
                step="0.01"
                placeholder="0.00"
                value={formData.bidAmount}
                onChange={(e) => handleInputChange("bidAmount", e.target.value)}
                className={`pl-7 ${
                  errors.bidAmount ? "border-destructive" : ""
                }`}
                disabled={isSubmitting}
              />
            </div>
            {errors.bidAmount && (
              <p className="text-sm text-destructive">{errors.bidAmount}</p>
            )}
            {jobBudget && (
              <p className="text-xs text-muted-foreground">
                Client&#39;s budget: ${jobBudget.toLocaleString()}
              </p>
            )}
          </div>

          {/* Proposed Rate (Optional) */}
          <div className="space-y-2">
            <label
              htmlFor="proposedRate"
              className="block text-sm font-medium text-foreground"
            >
              Hourly Rate (Optional)
            </label>
            <div className="relative">
              <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                <span className="text-muted-foreground text-sm">$</span>
              </div>
              <Input
                id="proposedRate"
                type="number"
                min="0"
                step="0.01"
                placeholder="0.00"
                value={formData.proposedRate}
                onChange={(e) =>
                  handleInputChange("proposedRate", e.target.value)
                }
                className={`pl-7 ${
                  errors.proposedRate ? "border-destructive" : ""
                }`}
                disabled={isSubmitting}
              />
              <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3">
                <span className="text-muted-foreground text-sm">/hour</span>
              </div>
            </div>
            {errors.proposedRate && (
              <p className="text-sm text-destructive">{errors.proposedRate}</p>
            )}
            <p className="text-xs text-muted-foreground">
              Optional: Your hourly rate for ongoing work or future projects
            </p>
          </div>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-3 pt-4">
            <Button
              size="sm"
              type="submit"
              disabled={isSubmitting}
              className="flex-1 sm:flex-none"
            >
              {isSubmitting ? (
                <>
                  <Icon name="Loader2" className="mr-2 h-4 w-4 animate-spin" />
                  Submitting Proposal...
                </>
              ) : (
                <>
                  <Icon name="Send" className="mr-2 h-4 w-4" />
                  Submit Proposal
                </>
              )}
            </Button>
            {onCancel && (
              <Button
                type="button"
                variant="outline"
                onClick={handleCancel}
                disabled={isSubmitting}
                className="flex-1 sm:flex-none"
              >
                Cancel
              </Button>
            )}
          </div>
        </form>
      </CardContent>
    </Card>
  );
};
