"use client";
import { useState, useEffect } from 'react';
import { useAuth } from '@/lib/auth/AuthContext';
import { jobService } from '@/api/jobService';

export interface ProposalCounts {
  total: number;
  pending: number;
  accepted: number;
  rejected: number;
}

export function useProposalCount() {
  const { user, isAuthenticated } = useAuth();
  const [counts, setCounts] = useState<ProposalCounts>({
    total: 0,
    pending: 0,
    accepted: 0,
    rejected: 0,
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchProposalCounts = async () => {
      if (!isAuthenticated || !user?.username || user.attributes?.['custom:role'] !== 'FREELANCER') {
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        setError(null);
        
        const proposals = await jobService.listMyProposals(user.username);
        
        const newCounts = {
          total: proposals.length,
          pending: proposals.filter(p => p.status === 'PENDING').length,
          accepted: proposals.filter(p => p.status === 'ACCEPTED').length,
          rejected: proposals.filter(p => p.status === 'REJECTED').length,
        };
        
        setCounts(newCounts);
      } catch (err) {
        console.error('Error fetching proposal counts:', err);
        setError(err instanceof Error ? err.message : 'Failed to fetch proposal counts');
      } finally {
        setLoading(false);
      }
    };

    fetchProposalCounts();
  }, [isAuthenticated, user?.username, user?.attributes]);

  const refresh = async () => {
    if (!isAuthenticated || !user?.username || user.attributes?.['custom:role'] !== 'FREELANCER') {
      return;
    }

    try {
      setError(null);
      const proposals = await jobService.listMyProposals(user.username);
      
      const newCounts = {
        total: proposals.length,
        pending: proposals.filter(p => p.status === 'PENDING').length,
        accepted: proposals.filter(p => p.status === 'ACCEPTED').length,
        rejected: proposals.filter(p => p.status === 'REJECTED').length,
      };
      
      setCounts(newCounts);
    } catch (err) {
      console.error('Error refreshing proposal counts:', err);
      setError(err instanceof Error ? err.message : 'Failed to refresh proposal counts');
    }
  };

  return {
    counts,
    loading,
    error,
    refresh,
  };
}
