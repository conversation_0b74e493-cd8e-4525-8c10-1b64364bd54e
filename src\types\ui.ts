import { ReactNode } from 'react';
import { LucideIcon } from 'lucide-react';

/**
 * UI component types and interfaces
 */

// Icon Types
export type IconName = keyof typeof import('lucide-react');

/**
 * Table Component Types
 */

export type AccessorFn<T> = (row: T) => ReactNode;

export type Cell<PERSON>enderer<T> = (value: unknown, row: T) => ReactNode;

/**
 * Base interface for table data
 * Extend this interface in your application to define specific data structures
 */
export interface TableData {
  /** Unique identifier for the row */
  id: string | number;
  
  /** Additional dynamic properties */
  [key: string]: unknown;
}

/**
 * Column configuration for the Table component
 * @template T The type of data in the table rows
 */
export interface Column<T> {
  /** Column header content */
  header: string | ReactNode;
  
  /** Property name or function to access the cell value */
  accessor: keyof T | AccessorFn<T>;
  
  /** Custom cell renderer function */
  cell?: CellRenderer<T>;
  
  /** Additional CSS classes for the column */
  className?: string;
  
  /** Additional CSS classes for the column header */
  headerClassName?: string;
  
  /** Additional CSS classes for the column cells */
  cellClassName?: string;
  
  /** Whether the column is sortable */
  sortable?: boolean;
  
  /** Width of the column */
  width?: string | number;
}

/**
 * Table component props
 * @template T The type of data in the table rows
 */
export interface TableProps<T> {
  /** Array of column configurations */
  columns: Column<T>[];
  
  /** Array of data to display in the table */
  data: T[];
  
  /** Callback when a row is clicked */
  onRowClick?: (row: T) => void;
  
  /** Configuration for the empty state */
  emptyState?: {
    title: string;
    description?: string;
    icon?: IconName;
    action?: ReactNode;
  };
  
  /** Additional CSS classes for the table */
  className?: string;
  
  /** Additional CSS classes for the table header */
  headerClassName?: string;
  
  /** Additional CSS classes for the table body */
  bodyClassName?: string;
  
  /** Additional CSS classes for table rows */
  rowClassName?: string | ((row: T, index: number) => string);
  
  /** Whether the table is in a loading state */
  isLoading?: boolean;
  
  /** Number of skeleton rows to show when loading */
  loadingRows?: number;
  
  /** Field to use as the unique key for each row */
  keyField?: keyof T;
  
  /** Sorting configuration */
  sortConfig?: {
    key: keyof T | null;
    direction: 'asc' | 'desc';
    onSort: (key: keyof T) => void;
  };
}

// Button Component Types
export interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link';
  size?: 'default' | 'sm' | 'lg' | 'icon';
  asChild?: boolean;
  children: ReactNode;
}

// Form Component Types
export interface FormProps extends React.FormHTMLAttributes<HTMLFormElement> {
  children: ReactNode;
}

export interface FormFieldProps {
  label?: string;
  error?: string;
  hint?: string;
  required?: boolean;
  className?: string;
  children: ReactNode;
}

export interface LabelProps extends React.LabelHTMLAttributes<HTMLLabelElement> {
  required?: boolean;
}

// Input Component Types
export interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  error?: boolean;
}

// Textarea Component Types
export interface TextareaProps extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  error?: boolean;
  resize?: 'none' | 'vertical' | 'horizontal' | 'both';
}

// Select Component Types
export interface SelectOption {
  value: string;
  label: string;
  disabled?: boolean;
}

export interface SelectProps extends React.SelectHTMLAttributes<HTMLSelectElement> {
  options: SelectOption[];
  placeholder?: string;
  error?: boolean;
}

// Radio Component Types
export interface RadioOption {
  value: string;
  label: string;
  description?: string;
  disabled?: boolean;
}

export interface RadioGroupProps {
  name: string;
  options: RadioOption[];
  value?: string;
  onChange?: (value: string) => void;
  className?: string;
  orientation?: 'horizontal' | 'vertical';
}

export interface RadioProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string;
  description?: string;
  error?: boolean;
}

// Dropdown Component Types
export interface DropdownItem {
  id: string;
  label: ReactNode;
  icon?: ReactNode;
  disabled?: boolean;
  onClick?: () => void;
}

export interface DropdownProps {
  trigger: ReactNode;
  items: DropdownItem[];
  align?: 'start' | 'center' | 'end';
  side?: 'top' | 'right' | 'bottom' | 'left';
  className?: string;
}

// Tabs Component Types
export interface TabItem {
  id: string;
  label: string;
  content: ReactNode;
  disabled?: boolean;
  badge?: string | number;
}

export interface TabsProps {
  items: TabItem[];
  defaultTab?: string;
  onChange?: (tabId: string) => void;
  className?: string;
  tabClassName?: string;
  contentClassName?: string;
}

// Pagination Component Types
export interface PaginationProps {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
  className?: string;
  showPageNumbers?: boolean;
  showPageInfo?: boolean;
  pageInfoTemplate?: (current: number, total: number) => string;
}

// Loading Component Types
export interface LoadingProps {
  size?: 'sm' | 'md' | 'lg';
  variant?: 'spinner' | 'dots' | 'pulse';
  className?: string;
  fullScreen?: boolean;
  message?: string;
}

export interface LoadingOverlayProps {
  isLoading: boolean;
  children: ReactNode;
  className?: string;
  message?: string;
  variant?: 'spinner' | 'dots' | 'pulse';
}

/**
 * Available icon sizes
 */
export const iconSizes = {
  xs: '0.75rem',    // 12px
  sm: '1rem',       // 16px
  md: '1.25rem',    // 20px
  lg: '1.5rem',     // 24px
  xl: '2rem',       // 32px
  '2xl': '3rem',    // 48px
} as const;

export interface IconProps extends Omit<React.SVGProps<SVGSVGElement>, 'ref'> {
  /** Name of the icon from the Lucide icon set */
  name: IconName;
  
  /** Size of the icon */
  size?: keyof typeof iconSizes;
  
  /** Additional CSS classes */
  className?: string;
}

// Sidebar Types
export interface SidebarItem {
  name: string;
  href: string;
  icon: LucideIcon | ReactNode;
  roles?: string[];
  children?: SidebarItem[];
  badge?: string | number;
}
