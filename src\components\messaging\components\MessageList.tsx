'use client';

import { Message, MessagingUser } from '../types';
import { cn } from '@/lib/utils';
import { formatDistanceToNow, format } from 'date-fns';
import { Avatar, AvatarImage, AvatarFallback } from '@/components/ui/Avatar';
import { Badge } from '@/components/ui/Badge';
import { Button } from '@/components/ui/Button';
import { ChevronUp, FileText, Download } from 'lucide-react';
import { useState, useCallback, useEffect, useRef } from 'react';

export interface MessageListProps {
  messages: Message[];
  currentUser: MessagingUser;
  otherUser: MessagingUser;
  loading?: boolean;
  onLoadMore?: () => void;
  hasMore?: boolean;
  className?: string;
}

export function MessageList({
  messages,
  currentUser,
  otherUser,
  loading = false,
  onLoadMore,
  hasMore = false,
  className,
}: MessageListProps) {
  const [showLoadMore, setShowLoadMore] = useState(false);
  const scrollRef = useRef<HTMLDivElement>(null);
  const [isNearTop, setIsNearTop] = useState(false);

  // Handle scroll to detect when user is near top for load more
  const handleScroll = useCallback(() => {
    if (!scrollRef.current) return;
    
    const { scrollTop, scrollHeight, clientHeight } = scrollRef.current;
    const isAtTop = scrollTop < 100;
    setIsNearTop(isAtTop);
    setShowLoadMore(isAtTop && hasMore && !loading);
  }, [hasMore, loading]);

  useEffect(() => {
    const scrollElement = scrollRef.current;
    if (scrollElement) {
      scrollElement.addEventListener('scroll', handleScroll);
      return () => scrollElement.removeEventListener('scroll', handleScroll);
    }
  }, [handleScroll]);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    if (scrollRef.current && !isNearTop) {
      scrollRef.current.scrollTop = scrollRef.current.scrollHeight;
    }
  }, [messages.length, isNearTop]);

  const getUserInfo = (senderId: string) => {
    return senderId === currentUser.id ? currentUser : otherUser;
  };

  const getStatusIcon = (status: Message['status']) => {
    switch (status) {
      case 'read':
        return '✓✓';
      case 'delivered':
        return '✓';
      case 'sent':
        return '○';
      default:
        return '';
    }
  };

  const handleFileDownload = (fileInfo: Message['fileInfo']) => {
    if (fileInfo?.url) {
      window.open(fileInfo.url, '_blank');
    }
  };

  if (loading && messages.length === 0) {
    return (
      <div className={cn('flex-1 flex items-center justify-center', className)}>
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-2"></div>
          <p className="text-sm text-muted-foreground">Loading messages...</p>
        </div>
      </div>
    );
  }

  if (messages.length === 0) {
    return (
      <div className={cn('flex-1 flex items-center justify-center', className)}>
        <div className="text-center p-6 max-w-md">
          <h3 className="text-lg font-medium mb-2">No messages yet</h3>
          <p className="text-sm text-muted-foreground">
            Start the conversation by sending a message below.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className={cn('flex-1 flex flex-col overflow-hidden', className)}>
      {/* Load More Button */}
      {showLoadMore && (
        <div className="p-4 border-b border-border bg-muted/50">
          <Button
            variant="ghost"
            size="sm"
            onClick={onLoadMore}
            disabled={loading}
            className="w-full"
          >
            <ChevronUp className="w-4 h-4 mr-2" />
            {loading ? 'Loading...' : 'Load older messages'}
          </Button>
        </div>
      )}

      {/* Messages List */}
      <div
        ref={scrollRef}
        className="flex-1 overflow-y-auto p-4 space-y-4"
      >
        {messages.map((message, index) => {
          const user = getUserInfo(message.senderId);
          const isCurrentUser = message.senderId === currentUser.id;
          const showDate = index === 0 || 
            format(new Date(message.timestamp), 'yyyy-MM-dd') !== 
            format(new Date(messages[index - 1].timestamp), 'yyyy-MM-dd');

          return (
            <div key={message.id}>
              {/* Date Separator */}
              {showDate && (
                <div className="flex items-center justify-center py-2">
                  <div className="bg-muted px-3 py-1 rounded-full">
                    <span className="text-xs text-muted-foreground font-medium">
                      {format(new Date(message.timestamp), 'MMMM d, yyyy')}
                    </span>
                  </div>
                </div>
              )}

              {/* Message Row */}
              <div className="flex items-start gap-3 p-3 rounded-lg hover:bg-muted/50 transition-colors">
                {/* Avatar */}
                <Avatar className="w-10 h-10 flex-shrink-0">
                  <AvatarImage src={user.avatar} alt={user.name} />
                  <AvatarFallback className="text-sm font-medium">
                    {user.name.charAt(0).toUpperCase()}
                  </AvatarFallback>
                </Avatar>

                {/* Message Content */}
                <div className="flex-1 min-w-0">
                  {/* Header */}
                  <div className="flex items-center gap-2 mb-1">
                    <span className="font-medium text-sm text-foreground">
                      {user.name}
                    </span>
                    <Badge 
                      variant={user.role === 'CLIENT' ? 'default' : 'secondary'}
                      className="text-xs px-2 py-0"
                    >
                      {user.role}
                    </Badge>
                    <span className="text-xs text-muted-foreground">
                      {format(new Date(message.timestamp), 'h:mm a')}
                    </span>
                    {isCurrentUser && (
                      <span className="text-xs text-muted-foreground ml-auto">
                        {getStatusIcon(message.status)}
                      </span>
                    )}
                  </div>

                  {/* Message Body */}
                  <div className="text-sm text-foreground">
                    {message.type === 'text' ? (
                      <p className="whitespace-pre-wrap break-words">
                        {message.content}
                      </p>
                    ) : message.type === 'file' && message.fileInfo ? (
                      <div className="flex items-center gap-2 p-3 bg-muted rounded-lg border">
                        <FileText className="w-5 h-5 text-muted-foreground flex-shrink-0" />
                        <div className="flex-1 min-w-0">
                          <p className="font-medium text-sm truncate">
                            {message.fileInfo.name}
                          </p>
                          <p className="text-xs text-muted-foreground">
                            {(message.fileInfo.size / 1024 / 1024).toFixed(2)} MB
                          </p>
                        </div>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleFileDownload(message.fileInfo)}
                          className="flex-shrink-0"
                        >
                          <Download className="w-4 h-4" />
                        </Button>
                      </div>
                    ) : (
                      <p className="text-sm italic text-muted-foreground">
                        {message.content}
                      </p>
                    )}
                  </div>
                </div>
              </div>
            </div>
          );
        })}

        {/* Loading indicator at bottom */}
        {loading && messages.length > 0 && (
          <div className="flex justify-center py-4">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
          </div>
        )}
      </div>
    </div>
  );
}
