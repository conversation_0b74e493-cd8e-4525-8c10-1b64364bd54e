'use client';

import React, { createContext, useContext, ReactNode, useCallback, useState } from 'react';
import { MessagingUser, Conversation, Message } from '@/components/messaging/types';

export interface MessagingContextType {
  isDrawerOpen: boolean;
  openMessagingDrawer: () => void;
  closeMessagingDrawer: () => void;
  toggleMessagingDrawer: () => void;
  currentUser: MessagingUser | null;
  setCurrentUser: (user: MessagingUser) => void;
  conversations: Conversation[];
  setConversations: (conversations: Conversation[]) => void;
  activeConversation: string | null;
  setActiveConversation: (conversationId: string | null) => void;
  sendMessage: (content: string) => Promise<void>;
  loadMoreMessages: (conversationId: string, before: Date) => Promise<Message[]>;
  onFileUpload?: (file: File) => Promise<string>;
}

const MessagingContext = createContext<MessagingContextType | undefined>(undefined);

export const MessagingProvider: React.FC<{
  children: ReactNode;
  currentUser: MessagingUser;
  initialConversations?: Conversation[];
  onSendMessage: (conversationId: string, content: string) => Promise<void>;
  onLoadMoreMessages: (conversationId: string, before: Date) => Promise<Message[]>;
  onFileUpload?: (file: File) => Promise<string>;
}> = ({ 
  children, 
  currentUser: initialUser, 
  initialConversations = [],
  onSendMessage: externalSendMessage,
  onLoadMoreMessages: externalLoadMoreMessages,
  onFileUpload: externalFileUpload,
}) => {
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const [currentUser, setCurrentUser] = useState<MessagingUser>(initialUser);
  const [conversations, setConversations] = useState<Conversation[]>(initialConversations);
  const [activeConversation, setActiveConversation] = useState<string | null>(null);

  const openMessagingDrawer = useCallback(() => {
    setIsDrawerOpen(true);
    document.body.style.overflow = 'hidden';
  }, []);

  const closeMessagingDrawer = useCallback(() => {
    setIsDrawerOpen(false);
    document.body.style.overflow = '';
  }, []);

  const toggleMessagingDrawer = useCallback(() => {
    setIsDrawerOpen(prev => {
      const newState = !prev;
      document.body.style.overflow = newState ? 'hidden' : '';
      return newState;
    });
  }, []);

  const sendMessage = useCallback(async (content: string) => {
    if (!activeConversation) return;
    
    await externalSendMessage(activeConversation, content);
    
    // Update the conversation's last message in the list
    setConversations(prev => 
      prev.map(conv => 
        conv.id === activeConversation
          ? {
              ...conv,
              lastMessage: {
                content,
                senderId: currentUser.id,
                timestamp: new Date(),
                status: 'sent',
                type: 'text',
              },
              updatedAt: new Date(),
            }
          : conv
      )
    );
  }, [activeConversation, currentUser.id, externalSendMessage]);

  const loadMoreMessages = useCallback(async (conversationId: string, before: Date) => {
    return await externalLoadMoreMessages(conversationId, before);
  }, [externalLoadMoreMessages]);

  // Cleanup effect to reset body overflow when component unmounts
  React.useEffect(() => {
    return () => {
      document.body.style.overflow = '';
    };
  }, []);

  return (
    <MessagingContext.Provider
      value={{
        isDrawerOpen,
        openMessagingDrawer,
        closeMessagingDrawer,
        toggleMessagingDrawer,
        currentUser,
        setCurrentUser,
        conversations,
        setConversations,
        activeConversation,
        setActiveConversation,
        sendMessage,
        loadMoreMessages,
        onFileUpload: externalFileUpload,
      }}
    >
      {children}
    </MessagingContext.Provider>
  );
};

export const useMessaging = (): MessagingContextType => {
  const context = useContext(MessagingContext);
  if (context === undefined) {
    throw new Error('useMessaging must be used within a MessagingProvider');
  }
  return context;
};
