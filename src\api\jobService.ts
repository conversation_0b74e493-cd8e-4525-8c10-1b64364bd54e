import type { Job, CreateJobInput, UpdateJobInput, JobFilter } from '../types/job';
import type { JobProposal, CreateJobProposalInput, ProposalStatus } from '../types/proposal';
import { graphQLClient } from '../lib/graphql/graphqlClient';
import { gql } from '@apollo/client';

const GET_JOB = gql`
  query GetJob($id: ID!) {
    getJob(id: $id) {
      id
      title
      description
      budget
      category
      deadline
      clientId
      createdAt
      updatedAt
    }
  }
`;

const LIST_JOBS = gql`
  query ListJobs($filter: ModelJobFilterInput, $limit: Int, $nextToken: String) {
    listJobs(filter: $filter, limit: $limit, nextToken: $nextToken) {
      items {
        id
        title
        description
        budget
        category
        deadline
        clientId
        createdAt
        updatedAt
      }
      nextToken
    }
  }
`;

const CREATE_JOB = gql`
  mutation CreateJob($input: CreateJobInput!) {
    createJob(input: $input) {
      id
      title
      description
      budget
      category
      deadline
      clientId
      createdAt
      updatedAt
    }
  }
`;

const UPDATE_JOB = gql`
  mutation UpdateJob($input: UpdateJobInput!) {
    updateJob(input: $input) {
      id
      title
      description
      budget
      category
      deadline
      updatedAt
    }
  }
`;

const DELETE_JOB = gql`
  mutation DeleteJob($input: DeleteJobInput!) {
    deleteJob(input: $input) {
      id
    }
  }
`;

const SUBMIT_PROPOSAL = gql`
  mutation CreateProposal($input: CreateProposalInput!) {
    createProposal(input: $input) {
      id
      jobId
      freelancerId
      coverLetter
      bidAmount
      proposedRate
      status
      createdAt
      updatedAt
    }
  }
`;

const UPDATE_PROPOSAL_STATUS = gql`
  mutation UpdateProposalStatus($input: UpdateProposalInput!) {
    updateProposal(input: $input) {
      id
      status
      updatedAt
    }
  }
`;

const WITHDRAW_PROPOSAL = gql`
  mutation WithdrawProposal($input: DeleteJobProposalInput!) {
    withdrawProposal(input: $input) {
      id
    }
  }
`;

interface CreateJobResponse {
  createJob: Job;
}

interface GetJobResponse {
  getJob: Job;
}

interface UpdateJobResponse {
  updateJob: Job;
}

interface DeleteJobResponse {
  deleteJob: {
    id: string;
  };
}

interface ListJobsResponse {
  data?: {
    listJobs: {
      items: Job[];
      nextToken?: string;
    };
  };
  listJobs?: {
    items: Job[];
    nextToken?: string;
  };
  items?: Job[];
  nextToken?: string;
}

interface SubmitProposalResponse {
  createProposal: JobProposal;
}

interface WithdrawProposalResponse {
  withdrawProposal: {
    id: string;
  };
}

function handleApiError(operation: string, error: unknown): never {
  const errorMessage = error instanceof Error ? error.message : 'Unknown error';
  console.error(`Error in ${operation}:`, error);
  throw new Error(`Failed to ${operation.toLowerCase()}: ${errorMessage}`);
}

export const jobService = {
  async createJob(input: CreateJobInput): Promise<Job> {
    try {
      const response = await graphQLClient.mutate<CreateJobResponse>(
        CREATE_JOB,
        { input }
      );
      return response.createJob;
    } catch (error) {
      return handleApiError('createJob', error);
    }
  },

  async getJob(id: string): Promise<Job> {
    try {
      const response = await graphQLClient.query<GetJobResponse>(GET_JOB, { id });
      return {
        ...response.getJob,
        status: response.getJob.status || 'OPEN'
      };
    } catch (error) {
      console.error('Error in getJob:', error);
      throw error;
    }
  },

  async updateJob(input: UpdateJobInput): Promise<Job> {
    try {
      const response = await graphQLClient.mutate<UpdateJobResponse>(
        UPDATE_JOB,
        { input }
      );
      return response.updateJob;
    } catch (error) {
      return handleApiError('updateJob', error);
    }
  },

  async deleteJob(id: string): Promise<void> {
    try {
      await graphQLClient.mutate<DeleteJobResponse>(
        DELETE_JOB,
        { input: { id } },
        { authMode: 'userPool' }
      );
    } catch (error) {
      handleApiError('deleteJob', error);
    }
  },

  async listJobs(filter?: JobFilter): Promise<{ items: Job[]; nextToken?: string }> {
    try {
      type TransformedFilter = Record<string, unknown> & {
        budget?: {
          ge?: number;
          le?: number;
        };
      };
      
      const transformedFilter: TransformedFilter = {};
      
      if (filter?.clientId) {
        transformedFilter.clientId = { eq: filter.clientId };
      }
      
      if (filter?.category) {
        transformedFilter.category = { eq: filter.category };
      }
      
      if (filter?.minBudget !== undefined || filter?.maxBudget !== undefined) {
        transformedFilter.budget = {};
        if (filter.minBudget !== undefined) {
          transformedFilter.budget.ge = filter.minBudget;
        }
        if (filter.maxBudget !== undefined) {
          transformedFilter.budget.le = filter.maxBudget;
        }
      }

      const response = await graphQLClient.query<ListJobsResponse>(
        LIST_JOBS,
        { 
          filter: Object.keys(transformedFilter).length > 0 ? transformedFilter : undefined 
        }
      );
      
      
      if (response?.data?.listJobs) {
        return response.data.listJobs;
      } else if (response?.listJobs) {
        return response.listJobs;
      } else if (Array.isArray(response?.items)) {
        return { 
          items: response.items as Job[], 
          nextToken: response.nextToken as string | undefined 
        };
      }
      
      console.warn('Unexpected response format from listJobs:', response);
      return { items: [], nextToken: undefined };
    } catch (error) {
      console.error('Error in listJobs:', error);
      return handleApiError('listJobs', error);
    }
  },

  async listMyJobs(clientId: string): Promise<Job[]> {
    try {
      const result = await this.listJobs({ clientId });
      
      return result?.items || [];
    } catch (error) {
      console.error('Error in listMyJobs:', error);
      return handleApiError('listMyJobs', error);
    }
  },

  async submitProposal(input: CreateJobProposalInput, freelancerId?: string): Promise<JobProposal> {
    try {
      if (!freelancerId) {
        throw new Error('Freelancer ID is required to submit a proposal');
      }
      
      const proposalInput = {
        jobId: input.jobId,
        freelancerId: freelancerId,
        bidAmount: input.bidAmount,
        coverLetter: input.coverLetter,
        proposedRate: input.proposedRate,
        status: 'PENDING' as const
      };

      const response = await graphQLClient.mutate<SubmitProposalResponse>(
        SUBMIT_PROPOSAL,
        { input: proposalInput }
      );
      return response.createProposal;
    } catch (error) {
      return handleApiError('submitProposal', error);
    }
  },

  async applyForJob(input: CreateJobProposalInput): Promise<JobProposal> {
    console.warn('applyForJob is deprecated. Use submitProposal instead.');
    return this.submitProposal(input);
  },

  async getJobProposals(jobId: string): Promise<JobProposal[]> {
    try {
      const response = await graphQLClient.query<{
        listProposals: {
          items: JobProposal[];
        } | null;
      }>(
        `query GetJobProposals($jobId: ID!) {
          listProposals(filter: { jobId: { eq: $jobId } }) {
            items {
              id
              jobId
              freelancerId
              coverLetter
              bidAmount
              proposedRate
              status
              createdAt
              updatedAt
            }
          }
        }`, { jobId });
      return response.listProposals?.items || [];
    } catch (error) {
      return handleApiError('getJobProposals', error);
    }
  },

  getJobApplications: async function(jobId: string): Promise<JobProposal[]> {
    console.warn('getJobApplications is deprecated. Use getJobProposals instead.');
    return this.getJobProposals(jobId);
  },

  updateApplicationStatus: async function(
    id: string, 
    status: ProposalStatus
  ): Promise<JobProposal> {
    console.warn('updateApplicationStatus is deprecated. Use updateProposalStatus instead.');
    return this.updateProposalStatus(id, status);
  },

  listMyApplications: async function(freelancerId: string): Promise<JobProposal[]> {
    console.warn('listMyApplications is deprecated. Use listMyProposals instead.');
    return this.listMyProposals(freelancerId);
  },

  async updateProposalStatus(
    id: string, 
    status: ProposalStatus
  ): Promise<JobProposal> {
    try {
      const response = await graphQLClient.mutate(
        UPDATE_PROPOSAL_STATUS,
        { input: { id, status } }
      ) as any;
      
      if (!response?.data?.updateProposal) {
        throw new Error('Failed to update proposal: Invalid response format');
      }
      
      return response.data.updateProposal;
    } catch (error) {
      console.error('Error in updateProposalStatus:', error);
      return handleApiError('updateProposalStatus', error);
    }
  },

  async listMyProposals(freelancerId: string): Promise<JobProposal[]> {
    try {
      console.log('Executing listMyProposals with freelancerId:', freelancerId);
      
      const proposalsQuery = `
        query ListMyProposals($freelancerId: ID!) {
          listProposals(filter: { freelancerId: { eq: $freelancerId } }) {
            items {
              id
              jobId
              freelancerId
              coverLetter
              bidAmount
              proposedRate
              status
              createdAt
              updatedAt
            }
          }
        }`;
      
      const proposalsVars = { freelancerId };
      console.log('Fetching proposals with query:', { query: proposalsQuery, variables: proposalsVars });
      
      const proposalsResponse = await graphQLClient.query<{
        listProposals: {
          items: Array<JobProposal & {
            job: {
              id: string;
              title: string;
              description: string;
              budget: number;
              status: string;
              isRemote: boolean;
              clientId: string;
              createdAt: string;
              updatedAt: string;
            };
          }>;
        };
      }>(proposalsQuery, proposalsVars);
      
      console.log('Proposals response:', JSON.stringify(proposalsResponse, null, 2));
      
      if (!proposalsResponse?.listProposals?.items) {
        console.warn('Unexpected response format:', proposalsResponse);
        return [];
      }
      
      const clientIds = Array.from(new Set(
        proposalsResponse.listProposals.items
          .map(p => p.job?.clientId)
          .filter(Boolean) as string[]
      ));
      
      const clientsQuery = `
        query ListClients($ids: [ID!]!) {
          listUsers(filter: { id: { in: $ids } }) {
            items {
              id
              name
              email
            }
          }
        }`;
      
      let clients: Array<{id: string; name: string; email: string}> = [];
      
      if (clientIds.length > 0) {
        console.log('Fetching client details for IDs:', clientIds);
        const clientsResponse = await graphQLClient.query<{
          listUsers: {
            items: Array<{
              id: string;
              name: string;
              email: string;
            }>;
          };
        }>(clientsQuery, { ids: clientIds });
        
        clients = clientsResponse?.listUsers?.items || [];
        console.log('Clients response:', clients);
      }
      
      const clientMap = new Map(clients.map(client => [client.id, client]));
      
      return proposalsResponse.listProposals.items.map(proposal => ({
        ...proposal,
        job: proposal.job ? {
          ...proposal.job,
          client: proposal.job.clientId ? clientMap.get(proposal.job.clientId) : undefined
        } : undefined
      }));
    } catch (error) {
      console.error('Error in listMyProposals:', error);
      return handleApiError('listMyProposals', error);
    }
  },


  async withdrawProposal(proposalId: string): Promise<void> {
    try {
      await graphQLClient.mutate<WithdrawProposalResponse>(
        WITHDRAW_PROPOSAL,
        { id: proposalId }
      );
    } catch (error) {
      handleApiError('withdrawProposal', error);
    }
  },

  async withdrawApplication(proposalId: string): Promise<void> {
    console.warn('withdrawApplication is deprecated. Use withdrawProposal instead.');
    return this.withdrawProposal(proposalId);
  },

  /**
   * Checks if a freelancer has already submitted a proposal for a specific job
   * @param jobId - The ID of the job to check
   * @param freelancerId - The ID of the freelancer
   * @returns Promise<boolean> - True if the freelancer has already submitted a proposal for this job
   */
  async hasSubmittedProposal(jobId: string, freelancerId: string): Promise<boolean> {
    try {
      const response = await graphQLClient.query<{
        listProposals: {
          items: Array<{ id: string }>;
        } | null;
      }>(
        `query HasSubmittedProposal($jobId: ID!, $freelancerId: ID!) {
          listProposals(
            filter: {
              jobId: { eq: $jobId },
              freelancerId: { eq: $freelancerId }
            },
            limit: 1
          ) {
            items {
              id
            }
          }
        }`,
        { jobId, freelancerId }
      );

      return (response.listProposals?.items?.length || 0) > 0;
    } catch (error) {
      console.error('Error checking if proposal exists:', error);
      return false;
    }
  }
};
