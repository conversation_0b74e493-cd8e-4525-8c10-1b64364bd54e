import type { User as BaseUser, UserRole } from "@/types/user";

/**
 * Message in a conversation
 */
export interface Message {
  /** Unique message identifier */
  id: string;
  
  /** Message content */
  content: string;
  
  /** ID of the user who sent the message */
  senderId: string;
  
  /** When the message was sent */
  timestamp: Date;
  
  /** Delivery status of the message */
  status: 'sent' | 'delivered' | 'read';
  
  /** Type of message content */
  type: 'text' | 'file' | 'system';
  
  /** File information (for file type messages) */
  fileInfo?: {
    name: string;
    type: string;
    size: number;
    url: string;
  };
}

/**
 * Conversation between users
 */
export interface Conversation {
  /** Unique conversation identifier */
  id: string;
  
  /** Users participating in the conversation */
  participants: MessagingUser[];
  
  /** Last message in the conversation */
  lastMessage?: Omit<Message, 'id'> & { id?: string };
  
  /** Number of unread messages */
  unreadCount: number;
  
  /** When the conversation was last updated */
  updatedAt: Date;
}

/**
 * User in the context of messaging
 */
export interface MessagingUser extends Pick<BaseUser, 'id' | 'name' | 'email'> {
  /** URL to user's avatar */
  avatar?: string;
  
  /** User's role in the messaging context */
  role: UserRole;
  
  /** Whether the user is currently online */
  isOnline: boolean;
  
  /** When the user was last seen */
  lastSeen?: Date;
}

export interface MessageInputProps {
  onSend: (content: string) => void;
  onTyping: (isTyping: boolean) => void;
  placeholder?: string;
  disabled?: boolean;
}

export interface MessageBubbleProps {
  /** The message to display */
  message: Message;
  
  /** Whether the message is from the current user */
  isCurrentUser: boolean;
  
  /** Whether to show the user's avatar */
  showAvatar: boolean;
  
  /** Whether to show the message timestamp */
  showTimestamp: boolean;
  
  /** The user who sent the message */
  user: MessagingUser;
}

export interface ConversationListProps {
  conversations: Conversation[];
  selectedConversationId?: string;
  onSelectConversation: (id: string) => void;
  currentUserId: string;
  loading?: boolean;
}

export interface MessageThreadProps {
  /** List of messages in the thread */
  messages: Message[];
  
  /** The currently logged-in user */
  currentUser: MessagingUser;
  
  /** The other user in the conversation */
  otherUser: MessagingUser;
  
  /** Whether the thread is currently loading messages */
  loading?: boolean;
  
  /** Callback to load more messages */
  onLoadMore: () => void;
  
  /** Whether there are more messages to load */
  hasMore: boolean;
  
  /** Whether the other user is currently typing */
  isTyping: boolean;
}
