'use client';

import { usePathname, useRouter } from 'next/navigation';
import { useAuth } from '@/lib/auth/AuthContext';
import { useEffect, useState } from 'react';
import { DashboardLayout } from '@/components/layouts/DashboardLayout';
import { Loading } from '@/components/ui';

export default function FreelancerLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const { isAuthenticated, user, loading: authLoading } = useAuth();
  const router = useRouter();
  const pathname = usePathname();
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    if (mounted && !authLoading) {
      if (!isAuthenticated) {
        router.push(`/login?redirect=${encodeURIComponent(pathname)}`);
      } else if (user?.attributes?.['custom:role'] !== 'FREELANCER') {
        const role = user?.attributes?.['custom:role']?.toLowerCase() || 'client';
        router.push(`/${role}/dashboard`);
      }
    }
  }, [isAuthenticated, authLoading, user, router, pathname, mounted]);

  if (authLoading || !mounted) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <Loading size="lg" />
      </div>
    );
  }

  const breadcrumbs = [
    { label: 'Home', href: '/freelancer/dashboard' },
  ];

  if (pathname !== '/freelancer/dashboard') {
    const pathParts = pathname.split('/').filter(Boolean);
    if (pathParts.length > 1) {
      const currentPage = pathParts[pathParts.length - 1];
      breadcrumbs.push({
        label: currentPage.charAt(0).toUpperCase() + currentPage.slice(1).replace(/-/g, ' '),
        href: pathname,
      });
    }
  }

  return (
    <DashboardLayout breadcrumbs={breadcrumbs}>
      {children}
    </DashboardLayout>
  );
}
