import { dirname } from "path";
import { fileURLToPath } from "url";
import { FlatCompat } from "@eslint/eslintrc";
import js from "@eslint/js";
import nextPlugin from "@next/eslint-plugin-next";
import tseslint from "@typescript-eslint/eslint-plugin";
import tsParser from "@typescript-eslint/parser";

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const compat = new FlatCompat({
  baseDirectory: __dirname,
});

const eslintConfig = [
  // Global ignores
  {
    ignores: ["**/amplify-codegen-temp/models/**"],
  },

  // Apply Next.js recommended config (includes core-web-vitals)
  ...compat.extends("next/core-web-vitals"),

  // Base JS recommended rules
  js.configs.recommended,

  // TypeScript configs
  {
    files: ["**/*.ts", "**/*.tsx"],
    languageOptions: {
      parser: tsParser,
    },
    plugins: {
      "@typescript-eslint": tseslint,
    },
    rules: {
      // disable base no-unused-vars, use TS-aware version
      "no-unused-vars": "off",
      "@typescript-eslint/no-unused-vars": ["error"],
    },
  },

  // Next.js plugin rules
  {
    plugins: {
      "@next/next": nextPlugin,
    },
    rules: {
      ...nextPlugin.configs.recommended.rules,
      ...nextPlugin.configs["core-web-vitals"].rules,
    },
  },

  // React runtime & globals fixes
  {
    files: ["**/*.ts", "**/*.tsx", "**/*.js", "**/*.jsx"],
    languageOptions: {
      globals: {
        React: "writable", // prevent "React is not defined" errors
      },
    },
    rules: {
      "react/react-in-jsx-scope": "off", // not needed in React 17+
      "no-undef": "off", // avoid false positives on React/JSX
    },
  },
];

export default eslintConfig;
