'use client';

import { useState, useEffect, useCallback } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { useAuth } from '@/lib/auth/AuthContext';
import { Job } from '@/types/job';
import { CreateJobProposalInput } from '@/types/proposal';
import { jobService } from '@/api/jobService';
import { formatDistanceToNow } from 'date-fns';
import { toast } from 'react-hot-toast';
import { Icon } from '@/components/ui/Icon';
import { Button } from '@/components/ui/Button';
import { DashboardLayout } from '@/components/layouts/DashboardLayout';
import { ContentHeader } from '@/components/layout/ContentHeader';
import { ProposalSubmissionForm } from '@/components/forms/ProposalSubmissionForm';

const JobDetailsPage = () => {
  const { id } = useParams<{ id: string }>();
  const { isAuthenticated, user, cognitoUserId, loading: authLoading } = useAuth();
  const router = useRouter();
  const [job, setJob] = useState<Job | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmittingProposal, setIsSubmittingProposal] = useState(false);
  const [hasSubmittedProposal, setHasSubmittedProposal] = useState(false);
  const [showProposalForm, setShowProposalForm] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const userRole = user?.attributes?.['custom:role'];
  const isFreelancer = userRole === 'FREELANCER';
  const isClient = userRole === 'CLIENT';
  
  useEffect(() => {
    console.log('--- Debug Info ---');
    console.log('User role:', userRole, 'isFreelancer:', isFreelancer, 'isClient:', isClient);
    console.log('Job data:', job);
    console.log('Job status:', job?.status);
    console.log('Has submitted proposal:', hasSubmittedProposal);
    console.log('Show proposal form:', showProposalForm);
    console.log('Is job open:', job?.status === 'OPEN');
    console.log('Is authenticated:', isAuthenticated);
    console.log('------------------');
  }, [userRole, isFreelancer, isClient, job, hasSubmittedProposal, showProposalForm, isAuthenticated]);

  const fetchJobAndProposalStatus = useCallback(async () => {
    if (!id) return;

    try {
      setIsLoading(true);
      console.log('Fetching job details for job ID:', id);
      const jobData = await jobService.getJob(id as string);
      console.log('Job data received:', jobData);
      
      const jobWithStatus = {
        ...jobData,
        status: jobData.status || 'OPEN'
      };
      
      setJob(jobWithStatus);

      if (isFreelancer && cognitoUserId) {
        console.log('Checking for existing proposal for freelancer ID:', cognitoUserId);
        const hasProposal = await jobService.hasSubmittedProposal(id as string, cognitoUserId);
        console.log('Has existing proposal:', hasProposal);
        setHasSubmittedProposal(hasProposal);
      } else {
        console.log('Not checking for proposals - isFreelancer:', isFreelancer, 'cognitoUserId:', cognitoUserId);
      }

      setError(null);
    } catch (err) {
      console.error('Error fetching job:', err);
      setError('Failed to load job details. Please try again later.');
    } finally {
      setIsLoading(false);
    }
  }, [id, isFreelancer, cognitoUserId]);

  useEffect(() => {
    fetchJobAndProposalStatus();
  }, [fetchJobAndProposalStatus]);

  const handleProposalSubmit = async (proposalData: CreateJobProposalInput) => {
    try {
      if (!cognitoUserId) {
        throw new Error('User not authenticated');
      }
      
      setIsSubmittingProposal(true);

      const submissionData = {
        ...proposalData,
        coverLetter: proposalData.coverLetter || ''
      };

      await jobService.submitProposal(submissionData, cognitoUserId);

      setHasSubmittedProposal(true);
      setShowProposalForm(false);

      toast.success('Proposal submitted successfully!');

      setTimeout(() => {
        router.push('/freelancer/proposals');
      }, 2000);

    } catch (err) {
      console.error('Error submitting proposal:', err);

      if (err instanceof Error) {
        if (err.message.includes('duplicate') || err.message.includes('already submitted')) {
          toast.error('You have already submitted a proposal for this job.');
          setHasSubmittedProposal(true);
        } else if (err.message.includes('authentication') || err.message.includes('unauthorized')) {
          toast.error('Authentication error. Please log in again.');
          router.push('/login');
        } else {
          toast.error('Failed to submit proposal. Please try again.');
        }
      } else {
        toast.error('Failed to submit proposal. Please try again.');
      }
    } finally {
      setIsSubmittingProposal(false);
    }
  };

  const handleShowProposalForm = () => {
    setShowProposalForm(true);
  };

  const handleCancelProposal = () => {
    setShowProposalForm(false);
  };

  if (authLoading || !isAuthenticated || isLoading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-full">
          <Icon name="Loader2" size="xl" className="animate-spin text-primary" />
        </div>
      </DashboardLayout>
    );
  }

  if (error) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-full px-4">
          <div className="w-full max-w-md p-6 text-center bg-card rounded-lg shadow-sm border">
            <div className="flex items-center justify-center w-12 h-12 mx-auto rounded-full bg-red-100">
              <Icon name="XCircle" size="lg" className="text-red-600" />
            </div>
            <h3 className="mt-3 text-lg font-medium text-foreground">Error</h3>
            <p className="mt-2 text-sm text-muted-foreground">{error}</p>
            <div className="mt-6">
              <button
                type="button"
                onClick={() => router.push('/')}
                className="inline-flex items-center px-4 py-2 text-sm font-medium text-white rounded-md shadow-sm bg-primary hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
              >
                Back
              </button>
            </div>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  if (!job) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-full px-4">
          <div className="w-full max-w-md p-6 text-center bg-card rounded-lg shadow-sm border">
            <div className="flex items-center justify-center w-12 h-12 mx-auto rounded-full bg-blue-100">
              <Icon name="Info" size="lg" className="text-blue-600" />
            </div>
            <h3 className="mt-3 text-lg font-medium text-foreground">Job Not Found</h3>
            <p className="mt-2 text-sm text-muted-foreground">
              The job you&#39;re looking for doesn&#39;t exist or has been removed.
            </p>
            <div className="mt-6">
              <button
                type="button"
                onClick={() => router.push('/')}
                className="inline-flex items-center px-4 py-2 text-sm font-medium text-white rounded-md shadow-sm bg-primary hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
              >
                Back
              </button>
            </div>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  const formattedDate = job.createdAt 
    ? formatDistanceToNow(new Date(job.createdAt), { addSuffix: true })
    : 'Date not available';
    
  const deadlineDate = job.deadline 
    ? new Date(job.deadline).toLocaleDateString()
    : 'No deadline specified';


  return (
    <DashboardLayout>
      <div className="w-full max-w-6xl mx-auto p-4 sm:p-6">
        <ContentHeader
          title={job.title}
          subtitle={job.category?.replace('_', ' ')}
          showBackButton={true}
          backButtonLabel="Back"
          breadcrumbs={[
            { label: 'Home', href: isFreelancer ? '/freelancer/dashboard' : '/client/dashboard' },
            { label: 'Jobs', href: isFreelancer ? '/freelancer/jobs' : '/client/jobs' },
            { label: job.title, current: true }
          ]}

        />
        
        <div className="w-full mx-auto py-4">
          <div className="bg-card rounded-lg shadow-sm border p-8">
            <div className="space-y-6">
              <div className="flex flex-wrap items-center gap-4 text-sm">
                <div className="flex items-center text-muted-foreground">
                  <Icon name="Briefcase" size="sm" className="mr-1.5 text-muted-foreground/70" />
                  {job.category?.replace('_', ' ')}
                </div>
                <div className="flex items-center text-muted-foreground">
                  <Icon name="DollarSign" size="sm" className="mr-1.5 text-muted-foreground/70" />
                  ${job.budget?.toLocaleString()}
                </div>
                  <div className="flex items-center text-muted-foreground">
                    <Icon name="Calendar" size="sm" className="mr-1.5 text-muted-foreground/70" />
                    {deadlineDate}
                  </div>
                  {job.isRemote && (
                    <div className="flex items-center text-sm text-green-600">
                      <Icon name="MapPin" size="sm" className="mr-1.5 text-green-500" />
                      Remote
                    </div>
                  )}
                  <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${
                    job.status === 'OPEN'
                      ? 'bg-green-100 text-green-800'
                      : job.status === 'IN_PROGRESS'
                      ? 'bg-blue-100 text-blue-800'
                      : job.status === 'COMPLETED'
                      ? 'bg-purple-100 text-purple-800'
                      : job.status === 'CANCELLED'
                      ? 'bg-red-100 text-red-800'
                      : 'bg-muted text-muted-foreground'
                  }`}>
                    {job.status?.replace(/_/g, ' ') || 'OPEN'}
                  </span>
                </div>
                
                <div className="space-y-4">
                  <h3 className="text-sm font-medium text-muted-foreground">DESCRIPTION</h3>
                  <div className="prose max-w-none text-foreground">
                    {job.description.split('\n').map((paragraph, i) => (
                      <p key={i} className="mb-4 last:mb-0">{paragraph}</p>
                    ))}
                  </div>
                </div>

              <div className="space-y-6">
                {job.skills && job.skills.length > 0 && (
                  <div className="space-y-2">
                    <h3 className="text-sm font-medium text-muted-foreground">REQUIRED SKILLS</h3>
                    <div className="flex flex-wrap gap-2">
                      {job.skills.map((skill, index) => (
                        <span
                          key={index}
                          className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-primary/10 text-primary"
                        >
                          {skill}
                        </span>
                      ))}
                    </div>
                  </div>
                )}

                {job.location && !job.isRemote && (
                  <div className="space-y-2">
                    <h3 className="text-sm font-medium text-muted-foreground">LOCATION</h3>
                    <div className="flex items-center text-foreground">
                      <Icon name="MapPin" size="sm" className="mr-2 text-muted-foreground/70" />
                      {job.location}
                    </div>
                  </div>
                )}

                <div className="pt-6 border-t border-border">
                  <h3 className="text-sm font-medium text-muted-foreground mb-3">CLIENT INFORMATION</h3>
                  <div className="flex items-center space-x-4">
                    <div className="flex-shrink-0">
                      <div className="w-10 h-10 rounded-full bg-muted flex items-center justify-center">
                        <Icon name="User" size="sm" className="text-muted-foreground" />
                      </div>
                    </div>
                    <div>
                      <h4 className="text-sm font-medium text-foreground">
                        {job.client?.name || 'Unknown Client'}
                      </h4>
                      <p className="text-sm text-muted-foreground">
                        Posted {formattedDate}
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Debug info - remove this after debugging */}
              {(() => {
                console.log('Debug info:', {
                  isFreelancer,
                  jobStatus: job.status,
                  hasSubmittedProposal,
                  userRole: user?.attributes?.['custom:role'],
                  showCondition: isFreelancer && job.status === 'OPEN'
                });
                return null;
              })()}

              {/* Debug info rendered in useEffect */}
              
              {isFreelancer && job?.status === 'OPEN' && (
                <div className="mt-8 pt-6 border-t border-border">
                  {hasSubmittedProposal ? (
                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
                      <div className="flex items-center">
                        <Icon name="CheckCircle" className="h-5 w-5 text-blue-600 mr-3" />
                        <div>
                          <h3 className="text-lg font-medium text-blue-900">Proposal Submitted</h3>
                          <p className="text-blue-700 mt-1">
                            You have already submitted a proposal for this job. You can view and manage your proposals in your dashboard.
                          </p>
                        </div>
                      </div>
                      <div className="mt-4">
                        <Button asChild variant="outline" size="sm">
                          <a href="/freelancer/proposals">
                            <Icon name="Briefcase" className="mr-2 h-4 w-4" />
                            View My Proposals
                          </a>
                        </Button>
                      </div>
                    </div>
                  ) : showProposalForm ? (
                    <ProposalSubmissionForm
                      jobId={id as string}
                      jobBudget={job.budget}
                      onSubmit={handleProposalSubmit}
                      onCancel={handleCancelProposal}
                      isSubmitting={isSubmittingProposal}
                    />
                  ) : (
                    <div className="bg-green-50 border border-green-200 rounded-lg p-6">
                      <div className="flex items-center justify-between">
                        <div>
                          <h3 className="text-lg font-medium text-green-900">Ready to Apply?</h3>
                          <p className="text-green-700 mt-1">
                            Submit your proposal to show the client why you&#39;re the perfect fit for this job.
                          </p>
                        </div>
                        <Button size='sm' onClick={handleShowProposalForm} className="ml-4">
                          <Icon name="Send" className="mr-2 h-4 w-4" />
                          Submit Proposal
                        </Button>
                      </div>
                    </div>
                  )}
                </div>
              )}

              {isClient && job?.clientId === user?.username && (
                <div className="mt-8 pt-6 border-t border-border flex justify-end space-x-3">
                    <button
                    type="button"
                    onClick={() => router.push(`/client/jobs/${id}/edit`)}
                    className="inline-flex items-center justify-center rounded-md border border-input bg-background px-4 py-2 text-sm font-medium shadow-sm hover:bg-accent hover:text-accent-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 cursor-pointer"
                  >
                    <Icon name="Edit" size="sm" className="mr-2" />
                    Edit Job
                  </button>
                    <button
                    type="button"
                    onClick={() => router.push(`/client/jobs/${id}/proposals`)}
                    className="inline-flex items-center justify-center rounded-md bg-primary px-4 py-2 text-sm font-medium text-primary-foreground shadow-sm hover:bg-primary/90 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 cursor-pointer"
                  >
                    <Icon name="Users" size="sm" className="mr-2" />
                    View Proposals
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
};

export default JobDetailsPage;
