import { UserRole } from './enums';

/**
 * User-related types and interfaces
 */

/**
 * Base user interface with common properties
 */
export interface BaseUser {
  /** Unique identifier for the user */
  id: string;
  
  /** User's full name */
  name: string;
  
  /** User's email address */
  email: string;
}

/**
 * Complete user profile with all properties
 */
export interface User extends BaseUser {
  /** User's role in the system */
  role: UserRole;
  
  /** URL to the user's profile photo */
  profilePhoto?: string;
  
  /** User's biography or description */
  bio?: string;
  
  /** List of user's skills (for freelancers) */
  skills?: string[];
  
  /** When the user account was created (ISO date string) */
  createdAt: string;
  
  /** When the user was last updated (ISO date string) */
  updatedAt?: string;
}

/**
 * User attributes used in authentication context
 */
export interface UserAttributes {
  /** User's email address */
  email: string;
  
  /** User's full name */
  name?: string;
  
  /** User's biography or description */
  bio?: string;
  
  /** URL to the user's profile photo */
  profilePhoto?: string;
  
  /** User's role in the system */
  role?: UserRole;
  
  /** List of user's skills (for freelancers) */
  skills?: string[];
  
  /** Custom attributes (for AWS Cognito) */
  'custom:role'?: UserRole;
  
  /** Allow string index for additional attributes */
  [key: string]: string | string[] | undefined;
}

/**
 * Input type for creating a new user
 */
export interface CreateUserInput {
  id: string;
  name: string;
  email: string;
  role: UserRole;
  profilePhoto?: string;
  bio?: string;
  skills?: string[] | null;
}

export interface UpdateUserInput {
  id: string;
  name?: string | null;
  email?: string | null;
  profilePhoto?: string | null;
  bio?: string | null;
  skills?: string[] | null;
}

/**
 * Authentication user with email and password
 */
export interface AuthUser {
  /** User's email address */
  email: string;
  
  /** User's password (only used during sign-up/sign-in) */
  password: string;
  
  /** Flag indicating if the user is from signup flow */
  fromSignup?: boolean;
  
  /** User's name (optional, used during signup) */
  name?: string;
  
  /** User's role (optional, used during signup) */
  role?: string;
}

// Export all types
// Note: We're not re-exporting types to avoid conflicts with the actual exports above
// Importing modules should use the direct exports (e.g., import { User } from '@/types/user')

export { UserRole } from './enums';
