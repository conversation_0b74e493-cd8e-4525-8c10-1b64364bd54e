import React from 'react';
import Image from 'next/image';
import { cn } from '@/lib/utils';

const Avatar = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn(
      'relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full',
      className
    )}
    {...props}
  />
));
Avatar.displayName = 'Avatar';

interface AvatarImageProps extends Omit<React.ComponentProps<typeof Image>, 'width' | 'height'> {
  className?: string;
}

const AvatarImage = React.forwardRef<
  React.ComponentRef<typeof Image>,
  AvatarImageProps
>(({ className, alt = '', ...props }, ref) => (
  <Image
    ref={ref}
    className={cn('aspect-square h-full w-full object-cover', className)}
    alt={alt}
    width={40}
    height={40}
    {...props}
  />
));
AvatarImage.displayName = 'AvatarImage';

const AvatarFallback = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn(
      'flex h-full w-full items-center justify-center rounded-full bg-muted text-sm font-medium text-muted-foreground',
      className
    )}
    {...props}
  />
));
AvatarFallback.displayName = 'AvatarFallback';

export { Avatar, AvatarImage, AvatarFallback };
