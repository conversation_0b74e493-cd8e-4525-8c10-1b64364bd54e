'use client';

import { useEffect } from 'react';
import { X } from 'lucide-react';
import { Button } from '@/components/ui/Button';
import { cn } from '@/lib/utils';
import { MessagingContainer } from './MessagingContainer';
import { MessagingUser, Conversation, Message } from './types';

export interface MessagingDrawerProps {
  isOpen: boolean;
  onClose: () => void;
  currentUser: MessagingUser;
  conversations: Conversation[];
  onSendMessage: (conversationId: string, content: string) => Promise<void>;
  onLoadMoreMessages: (conversationId: string, before: Date) => Promise<Message[]>;
  onConversationSelect?: (conversationId: string) => void;
  onFileUpload?: (file: File) => Promise<string>;
  className?: string;
}

export function MessagingDrawer({
  isOpen,
  onClose,
  currentUser,
  conversations,
  onSendMessage,
  onLoadMoreMessages,
  onConversationSelect,
  onFileUpload,
  className,
}: MessagingDrawerProps) {
  // Close drawer when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as HTMLElement;
      const drawer = document.getElementById('messaging-drawer');
      
      if (drawer && !drawer.contains(target) && isOpen) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = '';
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.body.style.overflow = '';
    };
  }, [isOpen, onClose]);

  if (!isOpen) return null;

  return (
    <>
      {/* Overlay */}
      <div className="fixed inset-0 bg-black/50 z-40" onClick={onClose} />
      
      {/* Drawer */}
      <div 
        id="messaging-drawer"
        className={cn(
          'fixed inset-y-0 right-0 w-full max-w-md bg-background shadow-xl z-50',
          'transform transition-transform duration-300 ease-in-out',
          isOpen ? 'translate-x-0' : 'translate-x-full',
          className
        )}
      >
        <div className="flex flex-col h-full">
          {/* Header */}
          <div className="flex items-center justify-between p-4 border-b border-border">
            <h2 className="text-xl font-semibold">Messages</h2>
            <Button
              variant="ghost"
              size="icon"
              onClick={onClose}
              className="text-muted-foreground hover:text-foreground"
            >
              <X className="h-5 w-5" />
            </Button>
          </div>
          
          {/* Messaging Container */}
          <div className="flex-1 overflow-hidden">
            <MessagingContainer
              currentUser={currentUser}
              initialConversations={conversations}
              onSendMessage={onSendMessage}
              onLoadMoreMessages={onLoadMoreMessages}
              onConversationSelect={onConversationSelect}
              onFileUpload={onFileUpload}
              showBackButton={false}
              onBack={onClose}
              className="h-full"
            />
          </div>
        </div>
      </div>
    </>
  );
}
