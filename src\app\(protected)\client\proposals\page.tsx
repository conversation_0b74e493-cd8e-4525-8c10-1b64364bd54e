'use client';

import { useState, useEffect, useCallback } from 'react';
import { useAuth } from '@/lib/auth/AuthContext';
import { jobService } from '@/api/jobService';
import { Button } from '@/components/ui';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Table, Column } from '@/components/ui/Table';
import { Icon } from '@/components/ui';
import { ContentHeader } from '@/components/layout/ContentHeader';
import { formatDistanceToNow } from 'date-fns';
import { Badge } from '@/components/ui/Badge';
import Link from 'next/link';

interface ProposalWithJob {
  id: string;
  jobId: string;
  freelancerId: string;
  coverLetter: string;
  bidAmount: number;
  proposedRate: number;
  status: string;
  createdAt: string;
  updatedAt: string;
  job?: {
    id: string;
    title: string;
    budget: number;
    status: string;
  };
  freelancer?: {
    id: string;
    name: string;
    email: string;
  };
}

const ITEMS_PER_PAGE = 10;

const ClientProposalsPage = () => {
  const { isAuthenticated, user, loading: authLoading } = useAuth();
  const [proposals, setProposals] = useState<ProposalWithJob[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [statusFilter, setStatusFilter] = useState<string>('');
  const [searchTerm, setSearchTerm] = useState('');

  const fetchProposals = useCallback(async () => {
    if (!user?.username) return;

    try {
      setIsLoading(true);
      // Get all jobs for this client first
      const clientJobs = await jobService.listMyJobs(user.username);
      
      // Get proposals for each job
      const allProposals: ProposalWithJob[] = [];
      
      for (const job of clientJobs) {
        try {
          const jobProposals = await jobService.getJobProposals(job.id);
          const proposalsWithJobInfo = jobProposals.map(proposal => ({
            ...proposal,
            job: {
              id: job.id,
              title: job.title,
              budget: job.budget,
              status: job.status,
            }
          }));
          allProposals.push(...proposalsWithJobInfo);
        } catch (error) {
          console.error(`Error fetching proposals for job ${job.id}:`, error);
        }
      }
      
      setProposals(allProposals);
    } catch (error) {
      console.error('Error fetching proposals:', error);
    } finally {
      setIsLoading(false);
    }
  }, [user?.username]);

  useEffect(() => {
    if (isAuthenticated && user?.attributes?.['custom:role'] === 'CLIENT') {
      fetchProposals();
    }
  }, [isAuthenticated, user, fetchProposals]);

  const handleStatusUpdate = async (proposalId: string, newStatus: string) => {
    try {
      await jobService.updateProposalStatus(proposalId, newStatus as any);
      await fetchProposals(); // Refresh the list
    } catch (error) {
      console.error('Error updating proposal status:', error);
    }
  };

  const filteredProposals = proposals.filter(proposal => {
    const matchesSearch = !searchTerm || 
      proposal.job?.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      proposal.coverLetter?.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = !statusFilter || proposal.status === statusFilter;
    
    return matchesSearch && matchesStatus;
  });

  const totalPages = Math.ceil(filteredProposals.length / ITEMS_PER_PAGE);
  const startIndex = (currentPage - 1) * ITEMS_PER_PAGE;
  const currentProposals = filteredProposals.slice(startIndex, startIndex + ITEMS_PER_PAGE);

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      PENDING: { variant: 'secondary' as const, label: 'Pending' },
      ACCEPTED: { variant: 'success' as const, label: 'Accepted' },
      REJECTED: { variant: 'destructive' as const, label: 'Rejected' },
      WITHDRAWN: { variant: 'outline' as const, label: 'Withdrawn' },
    };
    
    const config = statusConfig[status as keyof typeof statusConfig] || 
                  { variant: 'secondary' as const, label: status };
    
    return <Badge variant={config.variant}>{config.label}</Badge>;
  };

  const columns: Column<ProposalWithJob>[] = [
    {
      header: 'Job Title',
      accessor: 'job.title',
      cell: (value: unknown, row: ProposalWithJob) => (
        <div className="font-medium">
          <Link 
            href={`/client/jobs/${row.jobId}`}
            className="text-primary hover:underline"
          >
            {row.job?.title || 'Unknown Job'}
          </Link>
        </div>
      ),
    },
    {
      header: 'Freelancer',
      accessor: 'freelancer.name',
      cell: (value: unknown, row: ProposalWithJob) => (
        <div>
          {row.freelancer?.name || row.freelancerId}
        </div>
      ),
    },
    {
      header: 'Bid Amount',
      accessor: 'bidAmount',
      cell: (value: unknown) => (
        <span className="font-medium">${(value as number)?.toLocaleString() || 0}</span>
      ),
    },
    {
      header: 'Proposed Rate',
      accessor: 'proposedRate',
      cell: (value: unknown) => (
        <span>${(value as number)?.toLocaleString() || 0}/hr</span>
      ),
    },
    {
      header: 'Status',
      accessor: 'status',
      cell: (value: unknown) => getStatusBadge(value as string),
    },
    {
      header: 'Submitted',
      accessor: 'createdAt',
      cell: (value: unknown) => (
        <span className="text-muted-foreground">
          {formatDistanceToNow(new Date(value as string), { addSuffix: true })}
        </span>
      ),
    },
    {
      header: 'Actions',
      accessor: 'id',
      cell: (value: unknown, row: ProposalWithJob) => (
        <div className="flex space-x-2">
          <Button
            size="sm"
            variant="outline"
            onClick={() => {/* TODO: View proposal details */}}
          >
            View
          </Button>
          {row.status === 'PENDING' && (
            <>
              <Button
                size="sm"
                variant="default"
                onClick={() => handleStatusUpdate(row.id, 'ACCEPTED')}
              >
                Accept
              </Button>
              <Button
                size="sm"
                variant="destructive"
                onClick={() => handleStatusUpdate(row.id, 'REJECTED')}
              >
                Reject
              </Button>
            </>
          )}
        </div>
      ),
    },
  ];

  if (authLoading || !isAuthenticated) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Icon name="Loader2" size="xl" className="animate-spin" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <ContentHeader
        title="Proposals Received"
        description="Manage proposals from freelancers for your job postings"
      />

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Filter Proposals</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <input
                type="text"
                placeholder="Search by job title or cover letter..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
              />
            </div>
            <div>
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
              >
                <option value="">All Statuses</option>
                <option value="PENDING">Pending</option>
                <option value="ACCEPTED">Accepted</option>
                <option value="REJECTED">Rejected</option>
                <option value="WITHDRAWN">Withdrawn</option>
              </select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Proposals Table */}
      <Card>
        <CardContent className="p-0">
          {isLoading ? (
            <div className="flex items-center justify-center py-8">
              <Icon name="Loader2" size="lg" className="animate-spin" />
            </div>
          ) : (
            <Table
              columns={columns}
              data={currentProposals}
              emptyState={{
                title: "No proposals found",
                description: searchTerm || statusFilter 
                  ? "Try adjusting your search or filter criteria."
                  : "You haven't received any proposals yet.",
                icon: "FileText",
              }}
              pagination={{
                currentPage,
                totalPages,
                onPageChange: setCurrentPage,
                itemsPerPage: ITEMS_PER_PAGE,
                totalItems: filteredProposals.length,
              }}
            />
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default ClientProposalsPage;
