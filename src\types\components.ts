import React from 'react';
import { IconName } from './ui';

/**
 * UI Component Types
 * 
 * This file contains type definitions for all UI components in the application.
 * These types are used to ensure type safety and consistency across the application.
 */

// Button Component
export interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link';
  size?: 'default' | 'sm' | 'lg' | 'icon';
  asChild?: boolean;
  children: React.ReactNode;
}

// Input Component
export interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  error?: boolean;
}

// Textarea Component
export interface TextareaProps extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  error?: boolean;
  resize?: 'none' | 'vertical' | 'horizontal' | 'both';
}

// Select Component
export interface SelectOption {
  value: string;
  label: string;
  disabled?: boolean;
}

export interface SelectProps extends React.SelectHTMLAttributes<HTMLSelectElement> {
  options: SelectOption[];
  placeholder?: string;
  error?: boolean;
}

// Checkbox Component
export interface CheckboxProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string;
  description?: string;
  error?: boolean;
}

// Radio Component
export interface RadioOption {
  value: string;
  label: string;
  description?: string;
  disabled?: boolean;
}

export interface RadioGroupProps {
  name: string;
  options: RadioOption[];
  value?: string;
  onChange?: (value: string) => void;
  className?: string;
  orientation?: 'horizontal' | 'vertical';
  error?: boolean;
}

// Form Components
export interface FormProps extends React.FormHTMLAttributes<HTMLFormElement> {
  children: React.ReactNode;
}

export interface FormFieldProps {
  label?: string;
  error?: string;
  hint?: string;
  required?: boolean;
  className?: string;
  children: React.ReactNode;
  htmlFor?: string;
}

export interface LabelProps extends React.LabelHTMLAttributes<HTMLLabelElement> {
  required?: boolean;
}

// Dropdown Component
export interface DropdownItem {
  id: string;
  label: React.ReactNode;
  icon?: React.ReactNode;
  disabled?: boolean;
  onClick?: () => void;
}

export interface DropdownProps {
  trigger: React.ReactNode;
  items: DropdownItem[];
  align?: 'left' | 'right' | 'center' | 'start' | 'end';
  side?: 'top' | 'right' | 'bottom' | 'left';
  className?: string;
}

// Tabs Component
export interface TabItem {
  id: string;
  label: string;
  content: React.ReactNode;
  disabled?: boolean;
  badge?: string | number;
}

export interface TabsProps {
  items: TabItem[];
  defaultTab?: string;
  onChange?: (tabId: string) => void;
  className?: string;
  tabClassName?: string;
  contentClassName?: string;
}

// Loading Components
export interface LoadingProps {
  size?: 'sm' | 'md' | 'lg';
  variant?: 'spinner' | 'dots' | 'pulse';
  className?: string;
  fullScreen?: boolean;
  message?: string;
}

export interface LoadingOverlayProps {
  isLoading: boolean;
  children: React.ReactNode;
  className?: string;
  message?: string;
  variant?: 'spinner' | 'dots' | 'pulse';
}

// Avatar Component
export interface AvatarProps extends React.HTMLAttributes<HTMLDivElement> {
  src?: string;
  alt?: string;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  fallback?: string | React.ReactNode;
  className?: string;
}

// Badge Component
export interface BadgeProps extends React.HTMLAttributes<HTMLDivElement> {
  variant?: 'default' | 'secondary' | 'destructive' | 'outline' | 'success' | 'warning';
}

// Icon Component
export type IconSize = 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl';

export interface IconProps {
  /** Name of the icon from the Lucide library */
  name: IconName;
  /** Size of the icon */
  size?: IconSize;
  /** Additional CSS classes */
  className?: string;
  /** Additional icon props */
  [key: string]: unknown;
}

// Pagination Component
export interface PaginationProps {
  /** Current page number (1-based) */
  currentPage: number;
  /** Total number of pages */
  totalPages: number;
  /** Callback when page changes */
  onPageChange: (page: number) => void;
  /** Additional CSS classes */
  className?: string;
  /** Whether to show page number buttons */
  showPageNumbers?: boolean;
  /** Whether to show page info (e.g., "Page 1 of 5") */
  showPageInfo?: boolean;
  /** Custom page info template function */
  pageInfoTemplate?: (current: number, total: number) => string;
}

// Table Component
type AccessorFn<T> = (row: T) => React.ReactNode;
type CellRenderer<T> = (value: unknown, row: T) => React.ReactNode;

export interface TableData {
  id: string | number;
  [key: string]: string | number | boolean | null | undefined | Record<string, unknown> | Date;
}

export interface Column<T> {
  /** Column header content */
  header: string | React.ReactNode;
  /** Key to access data in the row object or a function to derive the value */
  accessor: keyof T | AccessorFn<T>;
  /** Custom cell renderer function */
  cell?: CellRenderer<T>;
  /** Additional CSS classes for the column */
  className?: string;
  /** Additional CSS classes for the column header */
  headerClassName?: string;
  /** Additional CSS classes for the column cells */
  cellClassName?: string;
  /** Whether the column is sortable */
  sortable?: boolean;
  /** Width of the column */
  width?: string | number;
}

export interface TableProps<T> {
  /** Array of column configurations */
  columns: Column<T>[];
  /** Array of data objects to display in the table */
  data: T[];
  /** Callback when a row is clicked */
  onRowClick?: (row: T) => void;
  /** Configuration for the empty state */
  emptyState?: {
    /** Title to display when there's no data */
    title: string;
    /** Optional description */
    description?: string;
    /** Optional icon name */
    icon?: IconName;
    /** Optional action button */
    action?: React.ReactNode;
  };
  /** Title of the table */
  title: string;
  /** Optional description */
  description?: string;
  /** Optional icon name */
  icon?: IconName;
  /** Optional action buttons */
  action?: React.ReactNode;
  /** Additional CSS classes for the table */
  className?: string;
  /** Additional CSS classes for the table header */
  headerClassName?: string;
  /** Additional CSS classes for the table body */
  bodyClassName?: string;
  /** Additional CSS classes for table rows, or a function that returns classes based on row data */
  rowClassName?: string | ((row: T, index: number) => string);
  /** Whether the table is in a loading state */
  isLoading?: boolean;
  /** Number of skeleton rows to show when loading */
  loadingRows?: number;
  /** Key to use as a unique identifier for each row (defaults to 'id') */
  keyField?: keyof T;
  /** Sorting configuration */
  sortConfig?: {
    /** Current sort key */
    key: keyof T | null;
    /** Current sort direction */
    direction: 'asc' | 'desc';
    /** Callback when sort changes */
    onSort: (key: keyof T) => void;
  };
}
