
# Freelance Marketplace Amplify Schema

enum UserRole {
  CLIENT
  FREELANCER
}

enum JobStatus {
  OPEN
  IN_PROGRESS
  COMPLETED
  CANCELLED
}

type User @model @auth(rules: [{ allow: private }]) {
  id: ID!
  name: String!
  email: AWSEmail!
  role: UserRole!
  profilePhoto: String
  bio: String
  skills: [String]
}

type Job @model @auth(rules: [{ allow: private }]) {
  id: ID!
  clientId: ID! # reference to User
  title: String!
  description: String!
  category: String!
  budget: Float!
  deadline: AWSDateTime
  isRemote: Boolean
  skills: [String]
  status: JobStatus @default(value: "OPEN")
  createdAt: AWSDateTime
  updatedAt: AWSDateTime
  proposals: [Proposal] @hasMany(indexName: "byJob", fields: ["id"])
}

enum ProposalStatus {
  PENDING
  ACCEPTED
  REJECTED
}

type Proposal @model @auth(rules: [{ allow: private }]) {
  id: ID!
  jobId: ID! @index(name: "byJob")
  freelancerId: ID! # reference to User
  bidAmount: Float!
  coverLetter: String
  status: ProposalStatus!
  proposedRate: Float
  createdAt: AWSDateTime
  updatedAt: AWSDateTime
  job: Job @belongsTo(fields: ["jobId"])
}

type Message @model @auth(rules: [{ allow: private }]) {
  id: ID!
  conversationId: ID!
  senderId: ID! # reference to User
  receiverId: ID! # reference to User
  messageText: String!
  timestamp: AWSDateTime!
}

enum ContractStatus {
  IN_PROGRESS
  COMPLETED
}

type Contract @model @auth(rules: [{ allow: private }]) {
  id: ID!
  jobId: ID! # reference to Job
  clientId: ID! # reference to User
  freelancerId: ID! # reference to User
  status: ContractStatus!
  createdAt: AWSDateTime!
}

enum PaymentStatus {
  PAID
  PENDING
}

enum PaymentMethod {
  STRIPE
  USDC
  MYVILLAGETOKEN
}

type Payment @model @auth(rules: [
  { allow: owner },
  { allow: groups, groups: ["admin"], operations: [read, update, delete] }
]) {
  id: ID!
  contractId: ID! # reference to Contract
  amount: Float!
  status: PaymentStatus!
  method: PaymentMethod!
}
